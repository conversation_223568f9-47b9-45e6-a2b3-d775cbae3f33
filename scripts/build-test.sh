#!/bin/bash
set -o errexit

# 编译前端
npm run web:build:test
# 编译后端
npm run server:build:test

rm -rf dist
mkdir dist

# 拷贝 opera 部署需要的环境变量
# cp deploy/env/setenv_test.sh dist/setenv.sh 

# 创建前端要拷贝到后端的文件夹
mkdir -p dist/web
# 拷贝前端的结果，到后端文件夹
cp -rf web/build/app dist/web
# 拷贝为了在下一步cdn的时候用
cp -rf web/build/mimg dist/mimg

# 拷贝server打包后的代码
cp -rf server/dist/* dist/
# 拷贝对应的package.json到dist目录
cp server/package.json dist
# PM2的文件
cp server/process.json dist
# 只安装生产环境需要的依赖
cd dist && npm install --production --registry http://npm.hz.infra.mail/registry/ --unsafe-perm=true --allow-root
