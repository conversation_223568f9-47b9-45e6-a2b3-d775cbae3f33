variables:
  SERVICE_CODE: 'tam-web'
  ARTIFACT_PATH: $CI_PROJECT_NAME.zip

stages:
  - package
  - upload

################################
#    前置检查
################################
before_script:
  - preCheck

################################
#    编译打包(test)
################################
package-fed-test:
  stage: package
  script:
    - pwd
    - sh scripts/install-ci.sh
    - npm run build:test
    - cp deploy/env/setenv_test.sh dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./*
  tags:
    - ci-front
  artifacts:
    paths:
      - $ARTIFACT_PATH
    expire_in: 1d
  cache:
    key: '$CI_PROJECT_NAME'
    paths:
      - web/node_modules/
      - server/node_modules/
  only:
    - /^(release|hotfix|dev).*$/

################################
#    编译打包(online)
################################
package-fed-online:
  stage: package
  script:
    - pwd
    - sh scripts/install-ci.sh
    - npm run build:online
    - cp deploy/env/setenv_online.sh dist/setenv.sh
    - cd dist && zip -rq ../$ARTIFACT_PATH ./*
  tags:
    - ci-front
  artifacts:
    paths:
      - $ARTIFACT_PATH
    expire_in: 1d
  cache:
    key: '$CI_PROJECT_NAME'
    paths:
      - web/node_modules/
      - server/node_modules/
  only:
    - /^(release|hotfix|master).*$/


################################
# 测试服上传代码制品  --autoDeploy=true --clusterId=0
################################
upload_artifacts-test:
  stage: upload
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=test --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION 

  tags:
    - ci-front
  only:
    - /^(release|hotfix|dev).*$/
  dependencies:
    - package-fed-test


################################
# 线上传代码制品
################################
upload_artifacts-online:
  stage: upload
  script:
    - pwd
    - version_tools time && CURRENT_TIMESTAMP=$(version_tools result)
    - version_tools version && PROJECT_VERSION=$(version_tools result)
    - ARTIFACT_VERSION="${PROJECT_VERSION}-${CI_COMMIT_REF_NAME##*/}-${CURRENT_TIMESTAMP}-${CI_PIPELINE_ID}"
    - eval opera truck $OPERA_ARGS --env=online --artifactPath=$ARTIFACT_PATH --artifactVersion=$ARTIFACT_VERSION
  tags:
    - ci-front
  only:
    - /^(release|hotfix|master).*$/
  dependencies:
    - package-fed-online