let developers: any[] = [];
if (window.location.host.indexOf('local.yx.mail.netease.com') > -1) {
  developers = ['wb.z<PERSON><PERSON><PERSON><PERSON>@mesg.corp.netease.com'];
}
export const developersList = developers;

export const BpmFlowNames = {
  CLOUD_IN_NUM: 'cloud_in_num', // 云内数量
  CLOUD_IN_SPEC: 'cloud_in_spec', // 云内规格
  CLOUD_OUT_NUM: 'cloud_out_num' // 云外数量
};

// 工单名称  类型
export const TopoName: any = {
  1: '云外实例个数缩减工单',
  2: '云内实例个数缩减工单',
  3: '云外实例规格缩减工单',
  4: '云内实例规格缩减工单'
};

export const OrderStatus: any = {
  1: '待负责人确认缩减规格',
  2: '待确认缩容完成',
  3: '已完成',
  4: '已拒绝',
  5: '已标记'
};

export const NodeStatus: any = {
  1: '审批中',
  2: '审批中',
  3: '已完结',
  4: '已拒绝'
};

export const TopoTypeValue = {
  CLOUD_OUT_NUM: 1, // 云外实例数量
  CLOUD_IN_NUM: 2, // 云内实例数量
  CLOUD_OUT_SPEC: 3, // 云外实例规格
  CLOUD_IN_SPEC: 4 // 云内实例规格
};
