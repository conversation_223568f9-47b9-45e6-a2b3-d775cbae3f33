export interface ISearchSelectProps {
    value?: string;
    onChange?: (s: any, option?: any, data?: any) => void;
    placeholder?: React.ReactNode;
    threshold?: number;
    fetchFunc: (v: string) => Promise<any>;
    defaultSearch?: boolean; // 是否初始化有值时触发搜索-编辑情况-以该值进行搜索
    defaultSearchAll?: boolean; // 是否初始化搜索全部
    disabled?: boolean;
    focusSearch?: boolean;
    deduplication?: boolean;
    isMultiple?: boolean;
}

export interface ISearchSelectOption {
    key: number | string;
    value: string;
    [key: string]: any;
}
