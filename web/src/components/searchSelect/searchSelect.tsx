/* eslint-disable */
import React, { useState, useCallback, useEffect } from 'react';
import { Select } from 'antd';
import debounce from 'lodash.debounce';
import { ISearchSelectProps, ISearchSelectOption } from './interface';

/**
 * 模糊搜索下拉框
 * @param props
 */
export const SearchSelect: React.FC<ISearchSelectProps> = (props: ISearchSelectProps) => {
    const [data, setData] = useState<ISearchSelectOption[]>([]);
    const [initSearch, setInitSearch] = useState<boolean>(false);
    const { placeholder, fetchFunc, threshold, value, onChange, defaultSearch, disabled, isMultiple } = props;

    useEffect(() => {
        const { defaultSearchAll, value } = props;
        if (defaultSearch && !initSearch && value) {
            setInitSearch(true);
            handleSearch(defaultSearchAll ? '' : value);
        }
    }, [props.value]);

    const handleChange = useCallback(
        (e: number | string, options) => {
            onChange && onChange(e, options || {}, data);
            if (!e) {
                // 点击清除的时候重新拉完整列表
                handleSearch('');
            }
        },
        [onChange, data]
    );

    const handleSearch = useCallback(
        debounce(async (v: string) => {
            // if (v) {

            // } else {
            //     setData([]);
            // }
            const res: ISearchSelectOption[] = await fetchFunc(v);
            setData(res || []);
        }, threshold || 300),
        [fetchFunc]
    );

    return (
        <Select
            allowClear
            showArrow
            showSearch
            defaultActiveFirstOption={false}
            disabled={disabled}
            filterOption={false}
            // mode={isMultiple ? "multiple" : null}
            getPopupContainer={triggerNode => triggerNode.parentElement}
            // notFoundContent={null}
            placeholder={<>{placeholder || '请输入'}</>}
            style={{ width: '100%', verticalAlign: 'top' }}
            value={value}
            onChange={handleChange}
            onSearch={handleSearch}
            onFocus={async (e: any) => {
                if (props.focusSearch) {
                    const res: ISearchSelectOption[] = await fetchFunc('');
                    setData(res);
                }
            }}>
            {data && data.length
                ? data.map((v: any) => (
                    <Select.Option key={v.key} value={v.key} opt={v}>
                        {v?.labelName ? v.labelName : v.name}
                    </Select.Option>
                ))
                : ''}
        </Select>
    );
};
