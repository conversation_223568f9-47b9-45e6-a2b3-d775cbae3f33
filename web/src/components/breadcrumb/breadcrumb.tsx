import React from 'react';
import { Breadcrumb as AntBreadcrumb } from 'antd';
import { Link } from 'react-router-dom';
import { IBreadcrumbProps } from './interface';
import './breadcrumb.scss';

/**
 * 带右侧搜索的面包屑
 */
export const Breadcrumb: React.FC<IBreadcrumbProps> = ({ breadCrumb }: IBreadcrumbProps) => {
  return (
    <div className="breadcrumb-wrap">
      <AntBreadcrumb separator=">">
        {breadCrumb.map(v => (
          <AntBreadcrumb.Item key={v.label}>
            {v.path ? <Link to={v.path}>{v.label}</Link> : <span>{v.label}</span>}
          </AntBreadcrumb.Item>
        ))}
      </AntBreadcrumb>
    </div>
  );
};
