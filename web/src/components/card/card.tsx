import React, { useState, useCallback } from 'react';
import classNames from 'classnames';
import { Row, Col, Tooltip } from 'antd';
import { InfoCircleOutlined, UpCircleOutlined } from '@ant-design/icons';
import { CardProps } from './card.interface';
import 'antd/lib/card/style';
import './card.scss';

const prefixCls = 'ant-card';
export const Card: React.FC<CardProps> = props => {
  const {
    className,
    extra,
    expand,
    headStyle = {},
    bodyStyle = {},
    title,
    loading,
    tipText,
    bordered = true,
    size = 'default',
    children
  } = props;
  const [isExpand, setExpand] = useState(true);
  const onHeadClick = useCallback(() => {
    if (!expand) {
      return;
    }
    setExpand(prevState => !prevState);
  }, []);
  const classString = classNames(prefixCls, 'ex-ant-card', className, {
    [`${prefixCls}-loading`]: loading,
    [`${prefixCls}-bordered`]: bordered,
    [`${prefixCls}-${size}`]: size !== 'default'
  });
  const loadingBlockStyle =
    bodyStyle.padding === 0 || bodyStyle.padding === '0px' ? { padding: 24 } : undefined;
  const loadingBlock = (
    <div className={`${prefixCls}-loading-content`} style={loadingBlockStyle}>
      <Row gutter={8}>
        <Col span={22}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
      </Row>
      <Row gutter={8}>
        <Col span={8}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
        <Col span={15}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
      </Row>
      <Row gutter={8}>
        <Col span={6}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
        <Col span={18}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
      </Row>
      <Row gutter={8}>
        <Col span={13}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
        <Col span={9}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
      </Row>
      <Row gutter={8}>
        <Col span={4}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
        <Col span={3}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
        <Col span={16}>
          <div className={`${prefixCls}-loading-block`} />
        </Col>
      </Row>
    </div>
  );
  const bodyClassNames = classNames(`${prefixCls}-body`, {
    [`${prefixCls}-body-collapse`]: !isExpand
  });
  return (
    <div className={classString}>
      {(title || extra) && (
        <div className={`${prefixCls}-head`} style={headStyle}>
          <div className={`${prefixCls}-head-wrapper`}>
            {title && (
              <div
                className={`${prefixCls}-head-title ${expand && `${prefixCls}-expand-head`}`}
                onClick={onHeadClick}>
                {title}
                {tipText && (
                  <Tooltip title={tipText}>
                    <InfoCircleOutlined
                      style={{ fontSize: '16px', marginLeft: '8px' }}
                      translate={null}
                    />
                  </Tooltip>
                )}
                {expand && (
                  <UpCircleOutlined
                    rotate={isExpand ? 0 : 180}
                    style={{ fontSize: '16px', marginLeft: '8px' }}
                    translate={null}
                  />
                )}
              </div>
            )}
            {extra && <div className={`${prefixCls}-extra`}>{extra}</div>}
          </div>
        </div>
      )}
      <div className={bodyClassNames} style={bodyStyle}>
        {loading ? loadingBlock : children}
      </div>
    </div>
  );
};
