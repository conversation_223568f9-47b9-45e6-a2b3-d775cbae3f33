import { axiosService } from '@sharkr/request';
import ApiHost from '../consts/api';
import snestHost from '../consts/host.config';

// 获取管理员工单列表
export function getAdminList(params: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/order/admin/getHistoryOrder`, params);
  return res;
}

// 获取工单列表
export function getHistoryOrderList(params: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/order/getHistoryOrder`, params);
  return res;
}

// 负责人确认缩减规格
export function submitReduceSpec(params: any): Promise<any> {
  const res = axiosService.postByJson(`${ApiHost}/xhr/order/submitReduceSpec`, params);
  return res;
}

// 确认缩容完成 xhr/order/submitReduceComplete
export function submitReduceComplete(params: any): Promise<any> {
  const res = axiosService.postByJson(`${ApiHost}/xhr/order/submitReduceComplete?flowId=${params}`);
  return res;
}

// 主机规格 /xhr/order/getHostSpec
// export function getHostSpec(params: any): Promise<any> {
//     const res = axiosService.get(`${ApiHost}/xhr/order/getHostSpec`, params);
//     return res;
// }

export function getHostSpec(params: any): Promise<any> {
  const res = axiosService.get(`${snestHost}/xhr/api/v1/listHostSpec`, params);
  return res;
}

// export const getServer = (code: string): Promise<any> => {
//     const searchParam: any = {
//         type: 'service',
//         qs: code,
//         page: 1,
//         size: 200
//     }
//     const res = axiosService.get('/xhr/cmdbServer/api/v2/ci/service', searchParam)
//     return res
// }

export const getServer = (params?: any): Promise<any> =>
    new Promise(resolve => {
        axiosService
            .get(`/xhr/cmdbServer/api/v2/service`, params)
            .then(res => {
                const { data } = res;
                const { result } = data
                resolve(
                    result && result.length && Array.isArray(result)
                        ? result.map(v => ({ key: v.code, value: v.name }))
                        : []
                );
            })
            .catch(() => {
                resolve([]);
            });
    });
