import { axiosService } from '@sharkr/request';
import ApiHost from '../consts/api';

// 查询资产大盘核心指标
export function getCoreMetric(params: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/assetDial/getCoreMetric`, params);
  return res;
}

// 查询机器来源概览
export function getMachineSource(params: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/assetDial/getMachineSource`, params);
  return res;
}

// 查询机器用途
export function getMachinePurpose(params: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/assetDial/getMachinePurpose`, params);
  return res;
}

// 查询月成本
export function getCostMoneyMonth(params: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/assetDial/getCostMoneyMonth`, params);
  return res;
}

// 查询cpu平均利用率
export function getCpuUtilizationRateMap(params: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/assetDial/getCpuUtilizationRateMap`, params);
  return res;
}

// 获取四级部门
export function getDepartment(params?: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/department/getDepartment`, params);
  return res;
}

// 获取部门资产总概括
export function getDepartmentAsset(params?: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/department/getDepartmentAsset`, params);
  return res;
}

// 查询部门资产top10 排序
export function getDepartmentAssetChart(params?: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/department/getDepartmentAssetChart`, params);
  return res;
}

// 查询某一部门的服务资源详情
export function getDepartmentAssetDetail(params?: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/department/getDepartmentAssetDetail`, params);
  return res;
}

// 获取部门服务负责人
export function getServiceOwner(params?: any): Promise<any> {
  const res = axiosService.get(`${ApiHost}/xhr/department/getServiceOwner`, params);
  return res;
}