export const doSomeThing = () => new Date();
/**
 * 去除对象中空值
 * @param obj
 */
export const clearNullValue = (obj: { [key: string]: string | number | undefined }) => {
  const newObj: any = {};
  for (const key in obj) {
    if (typeof obj[key] !== undefined && obj[key] !== '') {
      newObj[key] = obj[key];
    }
  }
  return newObj;
};

/**
 * 生成query字符串
 * @param obj
 * @returns {string}
 */
export const genQuery = (objparams: { [key: string]: string | number | undefined }) => {
  const obj: { [key: string]: string | number } = clearNullValue(objparams);
  let index = 0;
  let query = '?';
  Object.keys(obj).forEach(name => {
    if (obj[name] !== undefined && obj[name] !== null) {
      if (index !== 0) {
        query += '&';
      }
      const value = typeof obj[name] === 'string' ? encodeURIComponent(obj[name]) : obj[name];
      query += `${name}=${value}`;
      index += 1;
    }
  });
  return query;
};

/**
 * 生成query字符串
 * @param obj
 * @returns {string}
 */
export const genQueryNg = (objparams: { [key: string]: string | number | undefined }) => {
  const obj: { [key: string]: string | number } = clearNullValue(objparams);
  let index = 0;
  let query = '?';
  Object.keys(obj).forEach(name => {
    if (obj[name] !== undefined && obj[name] !== null) {
      if (index !== 0) {
        query += ';';
      }
      const value = obj[name];
      query += `${name}=${value}`;
      index += 1;
    }
  });
  return query;
};

/**
 * 提取query
 * @param string
 * @returns {obj}
 */
export const getQuery = (query: string): any => {
  const args: { [key: string]: string | number } = {};
  const pairs = query.replace(/\?/g, '').split('&');
  pairs.forEach(item => {
    const pos = item.indexOf('=');
    if (pos > -1) {
      const name = decodeURIComponent(item.substring(0, pos)).trim();
      const value = decodeURIComponent(item.substring(pos + 1)).trim();
      args[name] = value;
    }
  });
  return args;
};

export const getUrlPathname = (props: any, symbol: any = '?') => {
  return `${symbol}redirectUrl=${encodeURIComponent(
    `${props.history.location.pathname}${props.history.location.search}`
  )}`;
};

export const getRedirectPathname = (props: any, isdouble = false) => {
  let redirectUrl = null;
  redirectUrl = getQuery(props.location.search)?.redirectUrl
    ? isdouble
      ? getQuery(props.location.search)?.redirectUrl
      : decodeURIComponent(getQuery(props.location.search)?.redirectUrl)
    : null;
  return redirectUrl;
};

export const getOptionLabel = (options: any[] = [], value: any) => {
  if (!value && value !== 0) {
    return '';
  }
  return options.find(opt => opt.value === value)?.label;
};

export const getOptionName = (options: any[] = [], value: any) => {
  if (!value && value !== 0) {
    return '';
  }
  return options.find(opt => opt.value === value)?.name;
};

/**
 * 生成UUid
 * @returns {string}
 */
/* eslint-disable */
export function generateUUID() {
    let d = new Date().getTime();
    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c == 'x' ? r : (r & 0x7 | 0x8)).toString(16);
    });
    return uuid;
}

// -1 显示暂无
export function genNumberLabel(num: number, fixNum?: number) {
    const fix = (fixNum || fixNum === 0) ? fixNum : 2
    if (num === -1) {
        return '暂无'
    }
    return num.toFixed(fix)
}

