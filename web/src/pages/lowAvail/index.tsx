import { Button, Col, DatePicker, Form, Input, Row, Select, Table, Tooltip } from 'antd';
import React, { useState, useEffect, useRef } from 'react';
import { IcacSingleUserSelect } from '@eagler/umc-select-web';
import { SearchOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { RouteComponentProps } from 'react-router';
import moment from 'moment';
import { Breadcrumb } from '../../components/breadcrumb';
import { getDepartment } from '../../services/assetDial.service';
import { ListPagination } from '../../consts/type';
import { defaultPaginationValue } from '../../consts/common.config';
import { getlowUtilizationRateAsset } from '../../services/lowRate.service';
import { SearchSelect } from '../../components/searchSelect';
import { getServer } from '../../services/workflow.service';
import { genNumberLabel } from '../../utils';

const LowAvail: React.FC<RouteComponentProps> = (props: RouteComponentProps) => {
    const [formRef] = Form.useForm();
    // 部门下拉
    const [departmentList, setDepartmentList] = useState<any[]>([]);

    // 列表数据
    const [listData, setListData] = useState<{
        list: any[];
        pagination: ListPagination | null;
    }>({
        list: [],
        pagination: defaultPaginationValue
    });
    // 默认上月份
    const [defaultTime] = useState<number>(moment(moment().startOf('month').format('YYYY-MM')).valueOf())
    // 搜索参数
    const [searchParams, setSearchParams] = useState<any>({
        ...defaultPaginationValue,
        time: defaultTime
    });
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        // 获取部门下拉数据
        getDepartmenList();
        // 首次进入获取列表数据
        getList(searchParams);
    }, []);

    const columns = [
        {
            title: '服务编码',
            dataIndex: 'serviceCode',
            key: 'serviceCode'
        },
        {
            title: '总核数',
            dataIndex: 'coreNum',
            key: 'coreNum',
            sorter: true,
            render: (value: number) => genNumberLabel(value, 0)
        },
        // {
        //     title: '类型',
        //     dataIndex: 'serviceOriginType',
        //     key: 'serviceOriginType',
        //     render: (value: number, record: any) => {
        //         let text = '';
        //         switch (value) {
        //             case 1:
        //                 text = '云外';
        //                 break;
        //             case 2:
        //                 text = '云内';
        //                 break;
        //             default:
        //                 break;
        //         }
        //         return text;
        //     }
        // },
        {
            title: 'CPU平均使用率',
            dataIndex: 'cpuUtilizationRate',
            key: 'cpuUtilizationRate',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: 'CPU峰值使用率',
            dataIndex: 'cpuMaximum',
            key: 'cpuMaximum',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: 'load均值',
            dataIndex: 'loadUtilizationRate',
            key: 'loadUtilizationRate',
            sorter: true,
            render: (value: number) => genNumberLabel(value)
        },
        {
            title: 'load峰值',
            dataIndex: 'loadMaximum',
            key: 'loadMaximum',
            sorter: true,
            render: (value: number) => genNumberLabel(value)
        },
        {
            title: '内存平均使用率',
            dataIndex: 'memoryUtilizationRate',
            key: 'memoryUtilizationRate',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: '内存峰值使用率',
            dataIndex: 'memoryMaximum',
            key: 'memoryMaximum',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: '负责人',
            dataIndex: 'owner',
            key: 'owner'
        }
    ];

    // 获取部门下拉
    const getDepartmenList = () => {
        getDepartment().then(res => {
            if (res?.code === 200) {
                setDepartmentList(res?.data || []);
            }
        });
    };

    const getList = (params: any) => {
        setLoading(true);
        getlowUtilizationRateAsset(params)
            .then(res => {
                if (res?.code === 200) {
                    setListData({
                        list: res?.data?.result || [],
                        pagination: res?.data?.pagination
                    });
                }
                setLoading(false);
            })
            .catch(e => {
                console.log(e);
            });
    };

    const onSearch = () => {
        const formValue = formRef.getFieldsValue();
        console.log(formValue);
        const params = {
            ...searchParams,
            page: 1,
            ...formValue,
            time: moment(moment(formValue?.time || defaultTime)).valueOf()
        };
        setSearchParams({
            ...params
        });
        getList(params);
    };

    const tableChange = (pagination: any, filter: any, sorter: any) => {
        const params = {
            ...searchParams,
            page: pagination.current,
            size: pagination.pageSize
        };
        if (sorter.order === 'ascend') {
            params.sortOrder = 'asc';
            params.sortBy = sorter.field;
        } else if (sorter.order === 'descend') {
            params.sortOrder = 'desc';
            params.sortBy = sorter.field;
        } else {
            params.sortOrder = undefined;
            params.sortBy = undefined;
        }
        setSearchParams({
            ...params
        });
        getList(params);
    };

    const onReset = () => {
        formRef.resetFields();
    };

    return (
        <>
            <Breadcrumb breadCrumb={[{ label: '低利用率资产', path: '' }]} />
            <span style={{position: 'absolute', top: '16px', left: '87px'}}>
                <Tooltip arrowPointAtCenter placement="right" title="月cpu平均利用率<3%">
                    <QuestionCircleOutlined className="margin-l-base" translate={null} />
                </Tooltip>
            </span>
            <section className="sharkr-section" style={{ minWidth: '1250px' }}>
                <div className="sharkr-section-content">
                    <Form
                        form={formRef}
                        initialValues={{time: moment(defaultTime)}}
                        labelCol={{ span: 6 }}
                        wrapperCol={{ span: 16 }}>
                        <Row>
                            <Col span={8}>
                                <Form.Item label="部门" name="fourthDepartmentId">
                                    <Select allowClear showSearch optionFilterProp="children" placeholder="请选择" style={{ width: "300px" }}>
                                        {departmentList.map((item: any) => (
                                            <Select.Option key={item.departmentId} value={item.departmentId}>
                                                {item.departmentFullName}
                                            </Select.Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="服务类型" name="serviceOriginType">
                                    <Select allowClear showSearch optionFilterProp="children" placeholder="请选择">
                                        <Select.Option value={1}>云外服务</Select.Option>
                                        <Select.Option value={2}>云内服务</Select.Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="服务编码" name="serviceCode">
                                    <SearchSelect
                                        defaultSearch
                                        focusSearch
                                        placeholder="请输入服务code"
                                        fetchFunc={(e: string) => {
                                            return getServer({
                                                code: e,
                                                page: 1,
                                                size: 200
                                            });
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={8}>
                                <Form.Item label="服务负责人" name="owner">
                                    <IcacSingleUserSelect />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Form.Item label="月份" name="time">
                                    <DatePicker allowClear={false} picker="month" style={{ width: '100%' }} />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Col offset={6}>
                                    <Button
                                        className="margin-r-base"
                                        icon={<SearchOutlined translate={null} />}
                                        size="middle"
                                        type="primary"
                                        onClick={() => {
                                            onSearch();
                                        }}>
                                        搜索
                                    </Button>
                                    <Button
                                        size="middle"
                                        onClick={() => {
                                            onReset();
                                        }}>
                                        重置
                                    </Button>
                                </Col>
                            </Col>
                        </Row>
                    </Form>
                </div>
            </section>
            <section className="sharkr-section" style={{ minWidth: '1250px' }}>
                <div className="sharkr-section-content">
                    <Table
                        className="sharkr-table"
                        columns={columns}
                        dataSource={listData.list}
                        loading={loading}
                        pagination={{
                            showQuickJumper: true,
                            showSizeChanger: true,
                            total: listData.pagination?.total,
                            pageSize: searchParams.size,
                            // onChange: onPageChange,
                            current: Number(searchParams.page)
                        }}
                        rowKey="departmentFullName"
                        // scroll={{ x: 'max-content' }}
                        size="large"
                        onChange={tableChange}
                    />
                </div>
            </section>
        </>
    );
};

export default LowAvail;
