import React from 'react';
import { Route, Redirect, Switch } from 'react-router-dom';
import { CorePage404, CorePage500 } from '@sharkr/components';
import {
  OrgIdentityPermMgt,
  UserPermManagement,
  RoleManagement,
  PermManagement
} from '@eagler/icac-bu';
import { UmcAuthGuard } from '@eagler/authorizion';
import { Home } from './home';
import { ItemList, ItemEdit, ItemDetail } from './item';
import AdminList from './workflow/admin';
import Approve from './workflow/approve';
import WorkflowList from './workflow/list';
import Inventory from './inventory';
import AssetsOverAll from './assets';
import AssetsDetail from './assets/detail';
import LowAvail from './lowAvail';
import FAQ from './Faq';

export const AppRoute: React.FC = () => (
  <Switch>
    <Route component={Home} key="/home" path="/home" />
    <Route
      key="/icac/orgInfoList"
      path="/icac/orgInfoList"
      render={props => <UmcAuthGuard component={OrgIdentityPermMgt} />}
    />
    <Route
      key="/icac/userList"
      path="/icac/userList"
      render={props => <UmcAuthGuard component={UserPermManagement} />}
    />
    <Route
      key="/icac/roleList"
      path="/icac/roleList"
      render={props => <UmcAuthGuard component={RoleManagement} />}
    />
    <Route
      key="/icac/permList"
      path="/icac/permList"
      render={props => <UmcAuthGuard component={PermManagement} />}
    />
    <Route
      key="/workflow/admin"
      path="/workflow/admin"
      render={props => <UmcAuthGuard component={AdminList} />}
    />
    <Route
      key="/workflow/list"
      path="/workflow/list"
      render={props => <UmcAuthGuard component={WorkflowList} />}
    />
    <Route component={Approve} key="/workflow/approve" path="/workflow/approve" />
    <Route component={Inventory} key="/inventory" path="/inventory" />
    <Route component={AssetsOverAll} key="/assets/overall" path="/assets/overall" />
    <Route component={AssetsDetail} key="/assets/detail" path="/assets/detail" />
    <Route component={LowAvail} key="/lowAvail" path="/lowAvail" />
    <Route component={FAQ} key="/FAQ" path="/FAQ" />
    <Redirect exact from="/" to="/workflow/list" />
    <Route component={CorePage500} key="/500" path="/500" />
    <Route component={CorePage404} />
  </Switch>
);
