import React, { useState, useEffect, useRef } from 'react';
import { RouteComponentProps } from 'react-router';
import { Button, Col, DatePicker, Form, Input, Row, Select, Table, Tooltip } from 'antd';
import { SearchOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import ReactEcharts from 'echarts-for-react';
import { Breadcrumb } from '../../components/breadcrumb';
import { getDepartmentAsset, getDepartmentAssetChart } from '../../services/assetDial.service';
import { ListPagination } from '../../consts/type';
import { defaultPaginationValue } from '../../consts/common.config';
import { genNumberLabel, genQuery } from '../../utils';

const AssetsOverAll: React.FC<RouteComponentProps> = (props: RouteComponentProps) => {
    const [formRef] = Form.useForm();
    // const [departmentList, setDepartmentList] = useState<any[]>([])
    // 默认上月份
    const [defaultTime] = useState<number>(moment(moment().startOf('month').format('YYYY-MM')).valueOf())

    // 图表数据
    const [barData, setBarData] = useState({
        list1X: [],
        list1Data: [],
        list2X: [],
        list2Data: []
    });
    const [barSearchParams, setBarSearchparams] = useState<any>({
        time: defaultTime,
        topk: 10
    });

    // 列表数据
    const [listData, setListData] = useState<{
        list: any[];
        pagination: ListPagination | null;
    }>({
        list: [],
        pagination: defaultPaginationValue
    });
    // 搜索参数
    const [searchParams, setSearchParams] = useState<any>({
        ...defaultPaginationValue,
        time: defaultTime
    });
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        // 获取部门下拉数据
        // getDepartmenList()
        // 首次进入获取列表数据
        getList(searchParams);
        getBar(barSearchParams);
    }, []);

    const columns = [
        {
            title: '部门',
            dataIndex: 'departmentFullName',
            key: 'departmentFullName',
        },
        {
            title: '服务数',
            dataIndex: 'serviceNum',
            key: 'serviceNum',
            render: (value: number) => genNumberLabel(value, 0)
        },
        {
            title: '物理机',
            dataIndex: 'physicalMachineNum',
            key: 'physicalMachineNum',
            render: (value: number) => genNumberLabel(value, 0)
        },
        {
            title: '虚拟机',
            dataIndex: 'virtualMachineNum',
            key: 'virtualMachineNum',
            render: (value: number, record: any) => {
                if (value === -1 && record?.containerNum === -1) {
                    return '未知'
                } else if (value === -1) {
                    return record?.containerNum
                } else if (record?.containerNum === -1) {
                    return value
                } else {
                    return value + Number(record?.containerNum || 0)
                }
            }
        },
        {
            title: '核数',
            dataIndex: 'coreNum',
            key: 'coreNum',
            sorter: true,
            render: (value: number) => genNumberLabel(value, 0)
        },
        {
            title: '内存(G)',
            dataIndex: 'memorySize',
            key: 'memorySize',
            sorter: true,
            render: (value: number) => genNumberLabel(value, 0)
        },
        {
            title: '平均CPU利用率',
            dataIndex: 'cpuUtilizationRate',
            key: 'cpuUtilizationRate',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: '平均内存利用率',
            dataIndex: 'memoryUtilizationRate',
            key: 'memoryUtilizationRate',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        // {
        //     title: '均摊成本(元/月)',
        //     dataIndex: 'amortizedMoney',
        //     key: 'amortizedMoney',
        //     sorter: true,
        //     render: (value: number) => genNumberLabel(value, 1)
        // },
        // 资源浪费todo
        // {
        //     title: (
        //         <div>
        //             <span>资源浪费(元/月)</span>
        //             <Tooltip arrowPointAtCenter placement="right" title="desc">
        //                 <QuestionCircleOutlined style={{marginTop:' 3px', marginLeft: '3px'}} translate={null} />
        //             </Tooltip>
        //         </div>
        //     ),
        //     dataIndex: 'wasteMoney',
        //     key: 'wasteMoney',
        //     sorter: true,
        //     render: (value: number) => genNumberLabel(value, 1)
        // },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            render: (value: any, record: any) => (
                <>
                    <Button type="link" onClick={() => goDetail(record)}>
                        详细
                    </Button>
                </>
            )
        }
    ];

    const barOption = (xData: any[], yData: any[], tlt: string) => {
        return {
            title: {
                text: tlt
            },
            xAxis: {
                type: 'category',
                fontStyle: 'oblique',
                data: xData,
                axisLabel: {
                    interval: 0,
                    fontSize: 10,
                    formatter: function (value: any) {
                        return value.split('').join('\n');
                    }
                }
            },
            yAxis: {
                type: 'value'
            },
            grid: {
                top: 40
            },
            series: [
                {
                    data: yData,
                    type: 'bar'
                }
            ]
        };
    };

    // 获取部门下拉
    // const getDepartmenList = () => {
    //     getDepartment().then(res => {
    //         if (res?.code === 200) {
    //             setDepartmentList(res?.data || [])
    //         }
    //     })
    // }

    const getList = (params: any) => {
        setLoading(true);
        getDepartmentAsset(params)
            .then(res => {
                if (res?.code === 200) {
                    setListData({
                        list: res?.data?.result || [],
                        pagination: res?.data?.pagination
                    });
                }
                setLoading(false);
            })
            .catch(e => {
                console.log(e);
            });
    };

    const getBar = (params: any) => {
        getDepartmentAssetChart(params).then(res => {
            if (res?.code === 200) {
                if (res?.data?.length) {
                    const list1 = res.data.slice(0, 10);
                    const list2 = res.data.slice(10);
                    // 表1 x轴
                    const list1X = list1.map((item: any) => item.name);
                    // 表1 y轴
                    const list1Y = list1.map((item: any) => item.value === -1 ? 0 : item.value);
                    // 表2 x轴
                    const list2X = list2.map((item: any) => item.name);
                    // 表2 y轴
                    const list2Y = list2.map((item: any) => item.value);
                    setBarData({
                        list1X: list1X,
                        list1Data: list1Y,
                        list2X: list2X,
                        list2Data: list2Y
                    });
                } else {
                    setBarData({
                        list1X: [],
                        list1Data: [],
                        list2X: [],
                        list2Data: []
                    });
                }
            }
        });
    };

    const tableChange = (pagination: any, filter: any, sorter: any) => {
        const params = {
            ...searchParams,
            page: pagination.current,
            size: pagination.pageSize
        };
        if (sorter.order === 'ascend') {
            params.sortOrder = 'asc';
            params.sortBy = sorter.field;
        } else if (sorter.order === 'descend') {
            params.sortOrder = 'desc';
            params.sortBy = sorter.field;
        } else {
            params.sortOrder = undefined;
            params.sortBy = undefined;
        }
        setSearchParams({
            ...params
        });
        getList(params);
    };

    const onSearch = () => {
        const formValue = formRef.getFieldsValue();
        // 获取图表数据
        const barSearchFormVals = {
            time: moment(moment(formValue.time).format('YYYY-MM')).valueOf(),
            topk: 10
        };
        getBar(barSearchFormVals);
        setBarSearchparams(barSearchFormVals);

        // 获取列表数据
        const searchFormVals: any = {
            page: 1,
            size: searchParams.size,
            time: moment(moment(formValue.time).format('YYYY-MM')).valueOf()
        };
        setSearchParams(searchFormVals);
        getList(searchFormVals);
    };

    const goDetail = (record: any) => {
        const path = `/assets/detail${genQuery({
            fourthDepartmentId: record.fourthDepartmentId,
            time: barSearchParams.time
        })}`;
        window.open(`${window.location.origin}${window.location.pathname}#${path}`);
    };

    return (
        <>
            <Breadcrumb breadCrumb={[{ label: '部门资产', path: '' }]} />
            <section className="sharkr-section">
                <div className="sharkr-section-content">
                    <Form
                        form={formRef}
                        initialValues={{time: moment(defaultTime)}}
                        labelCol={{ span: 6 }}
                        wrapperCol={{ span: 16 }}>
                        <Row>
                            {/* <Col span={8}>
                                <Form.Item label="服务" name="serviceCode">
                                    <Select allowClear showSearch optionFilterProp="children" placeholder="请选择">
                                        {
                                            departmentList.map((item: any) => (
                                                <Select.Option key={item.fourthDepartmentId} value={item.fourthDepartmentId}>
                                                    {item.departmentFullName}
                                                </Select.Option>
                                            ))
                                        }
                                    </Select>
                                </Form.Item>
                            </Col> */}
                            <Col span={8}>
                                <Form.Item label="月份" name="time">
                                    <DatePicker allowClear={false} picker="month" style={{ width: '100%' }} />
                                </Form.Item>
                            </Col>
                            <Col span={8}>
                                <Col>
                                    <Button
                                        className="margin-r-base"
                                        icon={<SearchOutlined translate={null} />}
                                        size="middle"
                                        type="primary"
                                        onClick={() => {
                                            onSearch();
                                        }}>
                                        搜索
                                    </Button>
                                </Col>
                            </Col>
                        </Row>
                    </Form>
                </div>
            </section>
            <section className="sharkr-section">
                {/* <div className="sharkr-section-header">
                    <span className="sharkr-section-header-sub-title">图表如下:</span>
                </div> */}
                <div className="sharkr-section-content">
                    <div style={{ display: 'flex' }}>
                        {/* <div style={{ width: '49%', marginRight: '2%', paddingBottom: '40px' }}>
                            <ReactEcharts
                                option={barOption(barData.list1X, barData.list1Data, '资源浪费(元/月）')}
                                style={{ height: '450px' }}
                            />
                        </div> */}
                        <div style={{ width: '49%', paddingBottom: '40px' }}>
                            <ReactEcharts
                                option={barOption(barData.list2X, barData.list2Data, 'CPU平均使用率')}
                                style={{ height: '450px' }}
                            />
                        </div>
                    </div>
                </div>
            </section>
            <section className="sharkr-section">
                {/* <div className="sharkr-section-header">
                    <span className="sharkr-section-header-sub-title">详细数据如下:</span>
                </div> */}
                <div className="sharkr-section-content">
                    <Table
                        className="sharkr-table"
                        columns={columns}
                        dataSource={listData.list}
                        loading={loading}
                        pagination={{
                            showQuickJumper: true,
                            showSizeChanger: true,
                            total: listData.pagination?.total,
                            pageSize: searchParams.size,
                            // onChange: onPageChange,
                            current: Number(searchParams.page)
                        }}
                        rowKey="fourthDepartmentId"
                        // scroll={{ x: 'max-content' }}
                        size="large"
                        onChange={tableChange}
                    />
                </div>
            </section>
        </>
    );
};

export default AssetsOverAll;
