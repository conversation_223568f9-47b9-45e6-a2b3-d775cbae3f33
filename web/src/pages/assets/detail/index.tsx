import { Col, Row, Table, Tooltip } from 'antd';
import moment from 'moment';
import React, { useState, useEffect, useRef } from 'react';
import { RouteComponentProps, useLocation } from 'react-router';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { percentFormat } from '@sharkr/util';
import { IndicatorCardGroup } from '@eagler/dp-components';
import { Breadcrumb } from '../../../components/breadcrumb';
import { getDepartmentAsset, getDepartmentAssetDetail } from '../../../services/assetDial.service';
import { genNumberLabel, getQuery } from '../../../utils';
import { ListPagination } from '../../../consts/type';
import { defaultPaginationValue } from '../../../consts/common.config';

const AssetsDetail: React.FC<RouteComponentProps> = (props: RouteComponentProps) => {
    const location = useLocation();
    const { fourthDepartmentId, time } = getQuery(location.search);
    const [departmentDetail, setDepartmentDetail] = useState<any>();

    // 列表数据
    const [listData, setListData] = useState<{
        list: any[];
        pagination: ListPagination | null;
    }>({
        list: [],
        pagination: defaultPaginationValue
    });
    // 搜索参数
    const [searchParams, setSearchParams] = useState<any>({
        ...defaultPaginationValue,
        time,
        fourthDepartmentId
    });
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        getCoreQuota();
        // 列表数据
        getList(searchParams);
    }, [location.search]);

    // 获取核心指标
    const getCoreQuota = () => {
        getDepartmentAsset({
            time,
            fourthDepartmentId,
            page: 1,
            size: 1
        })
            .then(res => {
                if (res?.code === 200 && res?.data?.result?.length >= 1) {
                    const detail = res.data.result[0]
                    setDepartmentDetail({
                        ...detail,
                        coreNum: detail?.coreNum >= 0 ? detail.coreNum : 0,
                        serviceNum: detail?.serviceNum >= 0 ? detail.serviceNum : 0,
                        instanceNum: detail?.instanceNum >= 0 ? detail.instanceNum : 0,
                        cpuUtilizationRate: detail?.cpuUtilizationRate >= 0 ? detail.cpuUtilizationRate : 0,
                    });
                }
            })
            .catch(e => {
                console.log(e);
            });
    };

    const getList = (params: any) => {
        setLoading(true);
        getDepartmentAssetDetail(params)
            .then(res => {
                if (res?.code === 200) {
                    setListData({
                        list: res?.data?.result || [],
                        pagination: res?.data?.pagination
                    });
                }
                setLoading(false);
            })
            .catch(e => {
                console.log(e);
            });
    };

    const tableChange = (pagination: any, filter: any, sorter: any) => {
        const params = {
            ...searchParams,
            page: pagination.current,
            size: pagination.pageSize
        };
        if (sorter.order === 'ascend') {
            params.sortOrder = 'asc';
            params.sortBy = sorter.field;
        } else if (sorter.order === 'descend') {
            params.sortOrder = 'desc';
            params.sortBy = sorter.field;
        } else {
            params.sortOrder = undefined;
            params.sortBy = undefined;
        }
        setSearchParams({
            ...params
        });
        getList(params);
    };

    const machineConfig = [
        {
            keyName: 'coreNum',
            name: 'CPU核数',
            format: {
                digits: 0,
                unit: null,
                thousandsSeparator: true
            }
        },
        {
            keyName: 'serviceNum',
            name: '总服务',
            format: {
                digits: 0,
                unit: null,
                thousandsSeparator: true
            }
        },
        {
            keyName: 'instanceNum',
            name: '总实例数',
            format: {
                digits: 0,
                unit: null,
                thousandsSeparator: true,
                suffix: '台'
            }
        },
        {
            keyName: 'cpuUtilizationRate',
            name: '平均CPU利用率',
            format: {
                suffix: '%'
            }
        }
    ];

    const columns = [
        {
            title: '服务',
            dataIndex: 'serviceCode',
            key: 'serviceCode'
        },
        // {
        //     title: '云内/云外',
        //     dataIndex: 'serviceOriginType',
        //     key: 'serviceOriginType',
        //     render: (value: number, record: any) => {
        //         let text = '';
        //         switch (value) {
        //             case 1:
        //                 text = '云外';
        //                 break;
        //             case 2:
        //                 text = '云内';
        //                 break;
        //             default:
        //                 break;
        //         }
        //         return text;
        //     }
        // },
        {
            title: '总核数',
            dataIndex: 'coreNum',
            key: 'coreNum',
            sorter: true,
            render: (value: number) => genNumberLabel(value, 0)
        },
        {
            title: '实例数',
            dataIndex: 'instanceNum',
            key: 'instanceNum',
            sorter: true,
            render: (value: number) => genNumberLabel(value, 0)
        },
        {
            title: '平均CPU利用率',
            dataIndex: 'cpuUtilizationRate',
            key: 'cpuUtilizationRate',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: '内存平均利用率',
            dataIndex: 'memoryUtilizationRate',
            key: 'memoryUtilizationRate',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: 'CPU峰值使用率',
            dataIndex: 'cpuMaximum',
            key: 'cpuMaximum',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: '内存峰值使用率',
            dataIndex: 'memoryMaximum',
            key: 'memoryMaximum',
            sorter: true,
            render: (value: number, record: any) => genNumberLabel(value) === '暂无' ? '暂无' : `${value.toFixed(2)}%`
        },
        {
            title: '负责人',
            dataIndex: 'owner',
            key: 'owner'
        }
        // {
        //     title: '均摊成本(元/月)',
        //     dataIndex: 'amortizedMoney',
        //     key: 'amortizedMoney',
        //     sorter: true,
        //     render: (value: number) => genNumberLabel(value, 1)

        // },
        // 资源浪费todo
        // {
        //     title: (
        //         <div>
        //             <span>资源浪费(元/月)</span>
        //             <Tooltip arrowPointAtCenter placement="right" title="desc">
        //                 <QuestionCircleOutlined style={{marginTop:' 3px', marginLeft: '3px'}} translate={null} />
        //             </Tooltip>
        //         </div>
        //     ),
        //     dataIndex: 'wasteMoney',
        //     key: 'wasteMoney',
        //     sorter: true,
        //     render: (value: number) => genNumberLabel(value, 1)
        // }
    ];

    return (
        <>
            <Breadcrumb
                breadCrumb={[
                    { label: '部门资产', path: '' },
                    { label: '资产详情', path: '' }
                ]}
            />
            <Row className="margin-b-base" style={{ marginTop: '45px', fontSize: '18px' }}>
                <Col span={8}>
                    部门名称：
                    {departmentDetail?.departmentFullName || ''}
                </Col>
                <Col span={8}>
                    时间：
                    {moment(Number(time)).format('YYYY-MM')}
                </Col>
            </Row>
            <section className="sharkr-section">
                <div className="sharkr-section-header">
                    <span className="sharkr-section-header-title">核心指标</span>
                    <span className="sharkr-section-global-header-sub-title">
                        <Tooltip arrowPointAtCenter placement="right" title="核心指标desc">
                            <QuestionCircleOutlined className="margin-l-base" translate={null} />
                        </Tooltip>
                    </span>
                </div>
                <div className="sharkr-section-content">
                    <IndicatorCardGroup data={departmentDetail} groupConfig={machineConfig} />
                </div>
            </section>
            <section className="sharkr-section">
                <div className="sharkr-section-header">
                    <span className="sharkr-section-header-title">部门服务资源详情</span>
                </div>
                <div className="sharkr-section-content">
                    <Table
                        className="sharkr-table"
                        columns={columns}
                        dataSource={listData.list}
                        loading={loading}
                        pagination={{
                            showQuickJumper: true,
                            showSizeChanger: true,
                            total: listData.pagination?.total,
                            pageSize: searchParams.size,
                            // onChange: onPageChange,
                            current: Number(searchParams.page)
                        }}
                        rowKey="serviceCode"
                        // scroll={{ x: 'max-content' }}
                        size="large"
                        onChange={tableChange}
                    />
                </div>
            </section>
        </>
    );
};

export default AssetsDetail;
