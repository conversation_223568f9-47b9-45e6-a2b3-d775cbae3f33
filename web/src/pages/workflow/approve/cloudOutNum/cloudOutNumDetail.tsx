import React from 'react';
import { Row, Col, Form } from 'antd';
import { TopoName } from '../../../../consts/workflow.config';
import { genNumberLabel } from '../../../../utils';

interface IFlowDetail {
    orderDetail: any;
}

const formLayout = {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 }
};

const CloudOutNumDetail = (props: IFlowDetail) => {
    const { orderDetail } = props;
    const flowxData = orderDetail.flowxData;
    const flowMeta = flowxData?.flowMeta;
    const flowData = orderDetail?.flowData;
    const flowMetaData = flowData?.flowMetaData;
    const currNode = flowMetaData?.currentNodeId || '';
    const businessContent = flowxData?.businessContent ? JSON.parse(flowxData.businessContent) : null;

    return (
        <>
            <Form {...formLayout} className="label-break-all">
                <Row>
                    <Col span={8}>
                        <Form.Item className="f-wwb" label="工单编号">
                            {flowMeta?.flowId}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item className="f-wwb" label="工单类型">
                            {businessContent?.orderType ? TopoName[businessContent?.orderType] : ''}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item className="f-wwb" label="缩减服务">
                            {businessContent?.serviceCode ? businessContent?.serviceCode : '-'}
                        </Form.Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={8}>
                        <Form.Item className="f-wwb" label="实例现有规格">
                            {(businessContent?.cpuSpec && businessContent?.memorySpec) ? `${businessContent?.cpuSpec}c/${businessContent?.memorySpec}g` : ''}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item className="f-wwb" label="服务CPU平均使用率">
                            {businessContent?.cpuUtilizationRate ? genNumberLabel(Number(businessContent?.cpuUtilizationRate), 2): '暂无'}
                            {Number(businessContent?.cpuUtilizationRate) ? '%' : ''}
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item className="f-wwb" label="峰值使用率">
                            {businessContent?.cpuMaximum ? genNumberLabel(Number(businessContent?.cpuMaximum), 2) : '暂无'}
                            {Number(businessContent?.cpuMaximum) ? '%' : ''}
                        </Form.Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={8}>
                        <Form.Item className="f-wwb" label="实例现有个数">
                            {businessContent?.instanceNum ? businessContent?.instanceNum : ''}
                            个云外实例
                        </Form.Item>
                    </Col>
                    {currNode === '79910102' ? (
                        <Col span={8}>
                            <Form.Item className="f-wwb" label="建议缩减实例个数">
                                {businessContent?.proposedReduceInfo ? businessContent?.proposedReduceInfo : ''}
                                个云外实例
                            </Form.Item>
                        </Col>
                    ) : (
                        <Col span={8}>
                            <Form.Item className="f-wwb" label="实际需要缩减实例个数">
                                {businessContent?.actualReduceInfo ? businessContent?.actualReduceInfo : '0'}
                                个实例
                            </Form.Item>
                        </Col>
                    )}
                </Row>
            </Form>
        </>
    );
};

export default CloudOutNumDetail;
