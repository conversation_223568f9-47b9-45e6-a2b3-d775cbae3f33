import React, { useState } from 'react';
import { Button, Form, Radio, Input, Select, Tag, message, InputNumber, Row, Col } from 'antd';
import { getUserInfo } from '@sharkr/components';
import { submitReduceComplete, submitReduceSpec } from '../../../../services/workflow.service';

interface IApproveProps {
  orderDetail: any;
  flowId: any;
}

const formLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 }
};

const choices: Array<{
  label: string;
  value: boolean;
  disabled?: boolean;
}> = [
  {
    label: '同意缩减',
    value: true
  },
  {
    label: '不同意缩减',
    value: false
  }
];

const reconfirmChoics: Array<{
  label: string;
  value: boolean;
  disabled?: boolean;
}> = [
  {
    label: '已经完成缩容',
    value: true
  }
];

const FormItem = Form.Item;
const { TextArea } = Input;

const CloudInNumApprove = (props: IApproveProps) => {
  const { flowId, orderDetail } = props;
  const [form] = Form.useForm();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isPass, setIsPass] = useState(true);

  const flowData = orderDetail?.flowData;
  const flowMetaData = flowData?.flowMetaData;
  const currNode = flowMetaData?.currentNodeId || '';
  const flowxData = orderDetail?.flowxData;
  const businessContent = flowxData?.businessContent
    ? JSON.parse(flowxData?.businessContent)
    : null;

  // 审批按钮状态改变
  const radioChange = (e: any) => {
    if (e.target.value === choices[0].value) {
      setIsPass(true);
    } else {
      setIsPass(false);
    }
  };

  // 提交审批
  const doApprove = () => {
    form.validateFields().then(res => {
      const flowData = orderDetail.flowData;
      const currNode = flowData?.flowMetaData?.currentNodeId || '';

      if (currNode === '79910102') {
        const params = {
          flowId,
          agree: isPass,
          orderType: 2,
          actualReduceInfo: String(res.actualReduceInfo),
          refuseReason: isPass ? undefined : res.refuseReason
        };

        setIsSubmitting(true);
        submitReduceSpec(params)
          .then(result => {
            setIsSubmitting(false);
            if (result?.code === 200 && result.data) {
                message.success('提交成功');
                window.location.reload();
            } else {
                message.error(result?.message || '提交失败')
            }
          })
          .catch(e => {
            message.success('提交失败');
            setIsSubmitting(false);
          });
      } else if (currNode === '79910103') {
        setIsSubmitting(true);
        submitReduceComplete(flowId)
          .then(result => {
            setIsSubmitting(false);
            if (result?.code === 200 && result.data) {
                message.success('提交成功');
                window.location.reload();
            } else {
                message.error(result?.message || '提交失败')
            }
          })
          .catch(e => {
            setIsSubmitting(false);
            message.error('提交失败');
          });
      }
    });
  };

  return (
    <>
      <Form {...formLayout} form={form}>
        {/* 负责人确认节点 */}
        {currNode === '79910102' ? (
          <>
            <Row>
              <Col span={8}>
                <FormItem initialValue label="是否同意缩减实例" name="agree">
                  <Radio.Group onChange={radioChange}>
                    {choices.map(item => (
                      <Radio key={String(item.value)} value={item.value}>
                        {item.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={8}>
                {isPass ? (
                  <FormItem label="实际缩减实例个数" name="actualReduceInfo" rules={[{ required: true, message: '请填写' }]}>
                    <InputNumber min={1} step={1} />
                  </FormItem>
                ) : (
                  <FormItem
                    label="备注"
                    name="refuseReason"
                    rules={[{ required: true, message: '请填写备注' }]}>
                    <TextArea rows={4} />
                  </FormItem>
                )}
              </Col>
            </Row>
            <Row>
              <Col span={8}>
                <FormItem initialValue label="缩减实例参考文档">
                  <Button href={businessContent?.documentUrl} target="_blank" type="link">
                    {businessContent?.documentUrl}
                  </Button>
                </FormItem>
              </Col>
            </Row>
          </>
        ) : (
          ''
        )}
        {currNode === '79910103' ? (
          <>
            <Row>
              <Col span={8}>
                <FormItem initialValue label="缩减实例参考文档">
                  <Button href={businessContent?.documentUrl} target="_blank" type="link">
                    {businessContent?.documentUrl}
                  </Button>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col span={8}>
                <FormItem
                  label="是否已经完成缩容"
                  name="reconfirm"
                  rules={[{ required: true, message: '请选择' }]}>
                  <Radio.Group onChange={radioChange}>
                    {reconfirmChoics.map(item => (
                      <Radio key={String(item.value)} value={item.value}>
                        {item.label}
                      </Radio>
                    ))}
                  </Radio.Group>
                </FormItem>
              </Col>
            </Row>
          </>
        ) : (
          ''
        )}
        <Button disabled={isSubmitting} type="primary" onClick={doApprove}>
          提交
        </Button>
      </Form>
    </>
  );
};

export default CloudInNumApprove;
