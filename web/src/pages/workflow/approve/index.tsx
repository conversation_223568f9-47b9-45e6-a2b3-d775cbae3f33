import React, { useState, useEffect, useRef } from 'react';
import { RouteComponentProps, useHistory } from 'react-router';
import {
  WorkflowTopoComplete,
  WorkFlowDataX,
  WorkflowSource,
  WorkflowLog,
  WorkflowCreator
} from '@eagler/workflow';
import { getUserInfo } from '@sharkr/components';
import { Empty } from 'antd';
import { Card } from '../../../components/card';
import { getQuery } from '../../../utils';
import {
  BpmFlowNames,
  developersList,
  OrderStatus,
  TopoName
} from '../../../consts/workflow.config';
import { getHostSpec } from '../../../services/workflow.service';
import CloudInNumDetail from './cloudInNum/cloudInNumDetail';
import CloudInNumApprove from './cloudInNum/cloudInNumApprove';
import CloudOutNumDetail from './cloudOutNum/cloudOutNumDetail';
import CloudOutNumApprove from './cloudOutNum/cloudOutNumApprove';
import CloudInSpecDetail from './cloudInSpec/cloudInSpecDetail';
import CloudInSpecApprove from './cloudInSpec/cloudInSpecApprove';

const Approve: React.FC<RouteComponentProps> = (props: RouteComponentProps) => {
  const history = useHistory();
  const [flowId, setFlowId] = useState<string>();
  const [topologyId, setTopologyId] = useState<string>();
  const [isApprove, setIsApprove] = useState<boolean>(false); // 是否是审批人
  const [workFlowData, setWorkFlowData] = useState<WorkFlowDataX>();
  const [isEnd, setInEnd] = useState(false);
  const [refused, setIsRefused] = useState<boolean>(false); // 是否是拒绝节点 拒绝节点去编辑隐藏审批
  const [businessContent, setBusinessContent] = useState<any>();

  useEffect(() => {
    if (history?.location?.search) {
      const query = getQuery(history.location.search);
      if (query?.flowId) {
        setFlowId(query.flowId);
      }
      if (query?.topologyId) {
        setTopologyId(query.topologyId);
      }
    }
  }, [history]);

  const setApproveFlag = (tplOrderInfo: any) => {
    if (hasApprovePermission(tplOrderInfo)) {
      setIsApprove(true);
    } else {
      setIsApprove(false);
    }
  };

  const hasApprovePermission = (tplOrderInfo: any) => {
    if (developersList.includes(getUserInfo().email)) {
      return true;
    }
    if (
      tplOrderInfo?.flowData?.flowNodeMessage?.acceptorList &&
      tplOrderInfo.flowData.flowNodeMessage.acceptorList.includes(getUserInfo().email)
    ) {
      return true;
    } else {
      return false;
    }
  };
  const renderDetailComponent = () => {
    switch (topologyId) {
      case BpmFlowNames.CLOUD_IN_NUM:
        return <CloudInNumDetail orderDetail={workFlowData} />;
      case BpmFlowNames.CLOUD_OUT_NUM:
        return <CloudOutNumDetail orderDetail={workFlowData} />;
      case BpmFlowNames.CLOUD_IN_SPEC:
        return <CloudInSpecDetail orderDetail={workFlowData} />;

      default:
        return <Empty description={`不存在该类型工单: ${topologyId}`} />;
    }
  };

  const renderApproveComponent = () => {
    switch (topologyId) {
      case BpmFlowNames.CLOUD_IN_NUM:
        return <CloudInNumApprove flowId={flowId} orderDetail={workFlowData} />;
      case BpmFlowNames.CLOUD_OUT_NUM:
        return <CloudOutNumApprove flowId={flowId} orderDetail={workFlowData} />;
      case BpmFlowNames.CLOUD_IN_SPEC:
        return <CloudInSpecApprove flowId={flowId} orderDetail={workFlowData} />;
      default:
        return '';
    }
  };

  return (
    <div className="m-workflow-detail">
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">
            {businessContent?.orderType ? TopoName[businessContent?.orderType] : ''}
          </span>
        </div>
        <div className="sharkr-section-content">
          <Card expand title="拓扑流程图">
            <WorkflowSource
              params={{ topologyId, flowId }}
              url="/xhr/workflow/getAll.json"
              onFlowDataChange={(data: any) => {
                setWorkFlowData(data);
                setApproveFlag(data);
                const flowxData = data.flowxData;
                const flowData = data.flowData;
                // 工单节点9999 工单已结束
                if (flowData?.flowMetaData?.currentNodeId === '9999') {
                  setInEnd(true);
                }
                const tplBusinessContent = flowxData?.businessContent
                  ? JSON.parse(flowxData.businessContent)
                  : null;
                setBusinessContent(tplBusinessContent);
                if (Number(tplBusinessContent?.orderStatus) === 4) {
                  setIsRefused(true);
                }
              }}>
              <WorkflowTopoComplete hideInvalidedPath={false} workFlowData={workFlowData} />
            </WorkflowSource>
          </Card>
        </div>
      </section>
      <section className="sharkr-section">
        <div className="sharkr-section-content">
          <Card expand title="历史记录">
            <WorkflowLog
              excludeColumns={['files', 'reachTime', 'processTime']}
              sort={false}
              style={{ margin: 10 }}
              workFlowData={workFlowData}
            />
          </Card>
        </div>
      </section>
      <section className="sharkr-section">
        <div className="sharkr-section-content">
          <Card expand title="工单详情">
            <div className="section-block">
              {workFlowData ? (
                renderDetailComponent()
              ) : (
                <Empty description={`不存在该类型工单: ${topologyId}`} />
              )}
            </div>
            {// 审批页 && 审批人
            isApprove && !isEnd && !refused ? (
              <div className="section-block">{workFlowData ? renderApproveComponent() : ''}</div>
            ) : (
              ''
            )}
          </Card>
        </div>
      </section>
    </div>
  );
};

export default Approve;
