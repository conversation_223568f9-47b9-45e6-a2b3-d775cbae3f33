import React, { useState, useEffect, useRef } from 'react';
import { RouteComponentProps } from 'react-router';
import { Col, Form, Input, Row, Button, Table, Tabs } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { Popo } from '@eagler/popo';
import moment from 'moment';
import { Breadcrumb } from '../../../components/breadcrumb';
import { genQuery } from '../../../utils';
import { ListPagination } from '../../../consts/type';
import { defaultPaginationValue } from '../../../consts/common.config';
import { IListSearchParams } from '../admin/index.interface';
import { NodeStatus, OrderStatus, TopoName } from '../../../consts/workflow.config';
import { getKeyInfoRender } from '../admin/getKeyInfo';
import { getHistoryOrderList, getServer } from '../../../services/workflow.service';
import { SearchSelect } from '../../../components/searchSelect';
import { getUserInfo } from '@sharkr/components';

const { TabPane } = Tabs;

const WorkflowList: React.FC<RouteComponentProps> = (props: RouteComponentProps) => {
    const [formRef] = Form.useForm();

    const [listData, setListData] = useState<{
        list: any[];
        pagination: ListPagination | null;
    }>({
        list: [],
        pagination: defaultPaginationValue
    });

    // 搜索参数
    const [searchParams, setSearchParams] = useState<IListSearchParams>({
        ...defaultPaginationValue,
        opType: '1'
    });
    const [loading, setLoading] = useState(false);
    const [tabValue, setTabValue] = useState('1');

    const columns = [
        {
            title: '工单编号',
            dataIndex: 'flowId',
            key: 'flowId',
            width: '5%',
            render: (id: any, record: any) => (
                <Button
                    target="_blank"
                    type="link"
                    onClick={() => {
                        goFlowDetail(record);
                    }}>
                    {id}
                </Button>
            )
        },
        {
            title: '类型',
            dataIndex: 'orderType',
            key: 'orderType',
            width: '14%',
            render: (value: any) => <div>{value ? TopoName[value] : ''}</div>
        },
        {
            title: '工单关键信息',
            dataIndex: 'businessContentParse',
            key: 'businessContentParse',
            width: '16%',
            render: (value: any, record: any) => getKeyInfoRender(record)
        },
        {
            title: '提交时间',
            dataIndex: 'createTime',
            key: 'createTime',
            width: '13%',
            render: (createTime: number, record: any) =>
                createTime ? moment(createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            width: '13%',
            render: (updateTime: number, record: any) =>
                updateTime ? moment(updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'
        },
        {
            title: '状态',
            dataIndex: 'nodeId',
            key: 'nodeId',
            width: '6%',
            render: (value: number, record: any) =>
                record?.orderStatus ? NodeStatus[record.orderStatus] : '-'
        },
        {
            title: '等待操作',
            dataIndex: 'orderStatus',
            key: 'orderStatus',
            width: '16%',
            render: (value: number, record: any) => (value ? OrderStatus[value] : '-')
        },
        {
            title: '待操作人',
            dataIndex: 'operatorVOList',
            key: 'operatorVOList',
            width: '10%',
            render: (list: any[]) => (
                <div>
                    {list?.length
                        ? list.map((item, index) => (
                            <div key={index}>
                                <Popo email={item.uid} />
                            </div>
                        ))
                        : '/'}
                </div>
            )
        },
        {
            title: '操作',
            dataIndex: 'action',
            key: 'action',
            width: '5%',
            render: (value: any, record: any) => (
                <>
                    <Button type="link" onClick={() => goFlowDetail(record)}>
                        查看详情
                    </Button>
                </>
            )
        }
    ];

    const goFlowDetail = (record: any) => {
        const path = `/workflow/approve${genQuery({
            flowId: record.flowId,
            topologyId: record.topoId
        })}`;
        window.open(`${window.location.origin}${window.location.pathname}#${path}`);
    };

    const onSearch = (activeKey: string) => {
        const formVals = formRef.getFieldsValue();
        const searchFormVals: IListSearchParams = {
            page: 1,
            size: searchParams.size,
            serviceCode: formVals?.serviceCode,
            opType: activeKey
        };
        setTabValue(activeKey);
        setSearchParams(searchFormVals);
        getOrderList(searchFormVals);
    };

    useEffect(() => {
        getOrderList(searchParams);
    }, []);

    const getOrderList = (params: IListSearchParams) => {
        setLoading(true);
        getHistoryOrderList(params)
            .then(res => {
                if (res?.code === 200) {
                    setListData({
                        list: res?.data?.result || [],
                        pagination: res?.data?.pagination
                    });
                }
                setLoading(false);
            })
            .catch(e => {
                console.log(e);
            });
    };

    const onReset = () => {
        formRef.resetFields();
    };

    const tableChange = (pagination: any) => {
        const params = {
            ...searchParams,
            page: pagination.current,
            size: pagination.pageSize
        };
        setSearchParams({
            ...params
        });
        getOrderList(params);
    };

    const tabChange = (activeKey: string) => {
        console.log('enter')
        onSearch(activeKey);
    };

    return (
        <>
            <Breadcrumb
                breadCrumb={[
                    { label: '资产管理平台', path: '' },
                    { label: '工单列表', path: '' }
                ]}
            />
            <section className="sharkr-section">
                <div className="sharkr-section-header">
                    <span className="sharkr-section-header-title">我的缩减工单列表</span>
                </div>
                <div className="sharkr-section-content">
                    <Form
                        form={formRef}
                        labelCol={{ span: 6 }}
                        wrapperCol={{ span: 16 }}>
                        <Row>
                            <Col span={8}>
                                <Form.Item label="服务" name="serviceCode">
                                    <SearchSelect
                                        defaultSearch
                                        focusSearch
                                        placeholder="请输入服务code"
                                        fetchFunc={(e: string) => {
                                            return getServer({
                                                code: e,
                                                page: 1,
                                                size: 200,
                                                uid: getUserInfo().email
                                            });
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col offset={8} span={8}>
                                <Col offset={6}>
                                    <Button
                                        className="margin-r-base"
                                        icon={<SearchOutlined translate={null} />}
                                        size="middle"
                                        type="primary"
                                        onClick={() => {
                                            onSearch(tabValue);
                                        }}>
                                        搜索
                                    </Button>
                                    <Button
                                        size="middle"
                                        onClick={() => {
                                            onReset();
                                        }}>
                                        重置
                                    </Button>
                                </Col>
                            </Col>
                        </Row>
                    </Form>
                </div>
            </section>
            <section className="sharkr-section">
                <div className="sharkr-section-content">
                    <Tabs activeKey={tabValue} type="card" onChange={tabChange}>
                        <TabPane key={'1'} tab="待我审核" />
                        <TabPane key={'2'} tab="我处理的" />
                        <TabPane key={'3'} tab="已结束的" />
                    </Tabs>
                    <Table
                        bordered
                        className="sharkr-table"
                        columns={columns}
                        dataSource={listData.list}
                        loading={loading}
                        pagination={{
                            showQuickJumper: true,
                            showSizeChanger: true,
                            total: listData.pagination?.total,
                            pageSize: searchParams.size,
                            // onChange: onPageChange,
                            current: Number(searchParams.page)
                        }}
                        rowKey="flowId"
                        // scroll={{ x: 'max-content' }}
                        size="large"
                        onChange={tableChange}
                    />
                </div>
            </section>
        </>
    );
};

export default WorkflowList;
