import React, { useState, useEffect, useRef } from 'react';
import { Col, Form, Input, Row, Select, DatePicker, Button, Table } from 'antd';
import { Popo } from '@eagler/popo';
import { RouteComponentProps } from 'react-router';
import { SearchOutlined } from '@ant-design/icons';
import moment from 'moment';
import { ListPagination } from '../../../consts/type';
import { genQuery } from '../../../utils';
import { NodeStatus, OrderStatus, TopoName } from '../../../consts/workflow.config';
import { Breadcrumb } from '../../../components/breadcrumb';
import { defaultPaginationValue } from '../../../consts/common.config';
import { getAdminList, getServer } from '../../../services/workflow.service';
import { IListSearchParams } from './index.interface';
import { getKeyInfoRender } from './getKeyInfo';
import { SearchSelect } from '../../../components/searchSelect';

const Option = Select.Option;
const { RangePicker } = DatePicker;

const AdminList: React.FC<RouteComponentProps> = (props: RouteComponentProps) => {
  const [formRef] = Form.useForm();

  const [listData, setListData] = useState<{
    list: any[];
    pagination: ListPagination | null;
  }>({
    list: [],
    pagination: defaultPaginationValue
  });
  // 搜索参数
  const [searchParams, setSearchParams] = useState<IListSearchParams>({
    ...defaultPaginationValue
  });
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      title: '工单编号',
      dataIndex: 'flowId',
      key: 'flowId',
      width: '5%',
      render: (id: any, record: any) => (
        <Button
          target="_blank"
          type="link"
          onClick={() => {
            goFlowDetail(record);
          }}>
          {id}
        </Button>
      )
    },
    {
      title: '类型',
      dataIndex: 'orderType',
      key: 'orderType',
      width: '14%',
      render: (value: any) => <div>{value ? TopoName[value] : ''}</div>
    },
    {
      title: '工单关键信息',
      dataIndex: 'businessContentParse',
      key: 'businessContentParse',
      width: '16%',
      render: (value: any, record: any) => getKeyInfoRender(record)
    },
    {
      title: '提交时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: '13%',
      render: (createTime: number, record: any) =>
        createTime ? moment(createTime).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: '13%',
      render: (updateTime: number, record: any) =>
        updateTime ? moment(updateTime).format('YYYY-MM-DD HH:mm:ss') : '-'
    },
    {
      title: '状态',
      dataIndex: 'nodeId',
      key: 'nodeId',
      width: '6%',
      render: (value: number, record: any) =>
        record?.orderStatus ? NodeStatus[record.orderStatus] : '-'
    },
    {
      title: '等待操作',
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      width: '16%',
      render: (value: number, record: any) => (value ? OrderStatus[value] : '-')
    },
    {
      title: '待操作人',
      dataIndex: 'operatorVOList',
      key: 'operatorVOList',
      width: '10%',
      render: (list: any[]) => (
        <div>
          {list?.length
            ? list.map((item, index) => (
                <div key={index}>
                  <Popo email={item.uid} />
                </div>
              ))
            : '/'}
        </div>
      )
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: '5%',
      render: (value: any, record: any) => (
        <>
          <Button type="link" onClick={() => goFlowDetail(record)}>
            查看详情
          </Button>
        </>
      )
    }
  ];

  const goFlowDetail = (record: any) => {
    const path = `/workflow/approve${genQuery({
      flowId: record.flowId,
      topologyId: record.topoId
    })}`;
    window.open(`${window.location.origin}${window.location.pathname}#${path}`);
  };

  const onSearch = () => {
    const formVals = formRef.getFieldsValue();
    const searchFormVals: IListSearchParams = {
      page: 1,
      size: searchParams.size,
      serviceCode: formVals?.serviceCode,
      orderStatus: formVals?.orderStatus,
      orderType: formVals?.orderType,
      startTime: formVals?.timer?.length > 1 ? moment(formVals?.timer[0]).valueOf() : undefined,
      endTime: formVals?.timer?.length >= 2 ? moment(formVals?.timer[1]).valueOf() : undefined
    };
    setSearchParams(searchFormVals);
    getList(searchFormVals);
  };

  useEffect(() => {
    getList(searchParams);
  }, []);

  const getList = (params: IListSearchParams) => {
    setLoading(true);
    getAdminList(params)
      .then(res => {
        console.log(res);
        if (res?.code === 200) {
          setListData({
            list: res?.data?.result || [],
            pagination: res?.data?.pagination
          });
        }
        setLoading(false);
      })
      .catch(e => {
        console.log(e);
      });
  };

  const onReset = () => {
    formRef.resetFields();
  };

  const tableChange = (pagination: any) => {
    const params = {
      ...searchParams,
      page: pagination.current,
      size: pagination.pageSize
    };
    setSearchParams({
      ...params
    });
    getList(params);
  };

  return (
    <>
      <Breadcrumb
        breadCrumb={[
          { label: '资产管理平台', path: '' },
          { label: '工单列表', path: '' }
        ]}
      />
      <section className="sharkr-section">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">工单列表（管理员）</span>
        </div>
        <div className="sharkr-section-content">
          <Form
            form={formRef}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 16 }}>
            <Row>
              <Col span={8}>
                <Form.Item label="服务" name="serviceCode">
                    <SearchSelect
                        defaultSearch
                        focusSearch
                        placeholder="请输入服务code"
                        fetchFunc={(e: string) => {
                            return getServer({
                                code: e,
                                page: 1,
                                size: 200
                            });
                        }}
                    />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="工单状态" name="orderStatus">
                  <Select allowClear style={{ width: '100%' }}>
                    <Option value={1}>待负责人确认缩减规格</Option>
                    <Option value={2}>待确认缩容完成</Option>
                    <Option value={4}>已拒绝</Option>
                    <Option value={3}>已完成</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="工单类型" name="orderType">
                  <Select allowClear style={{ width: '100%' }}>
                    <Option value={2}>云内实例个数缩减工单</Option>
                    <Option value={4}>云内实例规格缩减工单</Option>
                    <Option value={1}>云外实例个数缩减工单</Option>
                    <Option value={3}>云外实例规格缩减工单</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row>
              <Col span={8}>
                <Form.Item label="创建时间" name="timer">
                  <RangePicker format="YYYY-MM-DD HH:mm" showTime={{ format: 'HH:mm' }} />
                </Form.Item>
              </Col>
              <Col offset={8} span={8}>
                <Col offset={6}>
                  <Button
                    className="margin-r-base"
                    icon={<SearchOutlined translate={null} />}
                    size="middle"
                    type="primary"
                    onClick={() => {
                      onSearch();
                    }}>
                    搜索
                  </Button>
                  <Button
                    size="middle"
                    onClick={() => {
                      onReset();
                    }}>
                    重置
                  </Button>
                </Col>
              </Col>
            </Row>
          </Form>
        </div>
      </section>
      <section className="sharkr-section">
        <div className="sharkr-section-content">
          <Table
            bordered
            className="sharkr-table"
            columns={columns}
            dataSource={listData.list}
            loading={loading}
            pagination={{
              showQuickJumper: true,
              showSizeChanger: true,
              total: listData.pagination?.total,
              pageSize: searchParams.size,
              // onChange: onPageChange,
              current: Number(searchParams.page)
            }}
            rowKey="flowId"
            // scroll={{ x: 'max-content' }}
            size="large"
            onChange={tableChange}
          />
        </div>
      </section>
    </>
  );
};

export default AdminList;
