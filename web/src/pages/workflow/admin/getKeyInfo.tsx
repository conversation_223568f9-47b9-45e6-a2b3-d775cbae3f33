import React from 'react';
import { TopoTypeValue } from '../../../consts/workflow.config';

export const getKeyInfoRender = (record: any) => {
  let dom;
  switch (record.orderType) {
    case TopoTypeValue.CLOUD_OUT_NUM:
      dom = getkeyInfo(record, '个数');
      break;
    case TopoTypeValue.CLOUD_IN_NUM:
      dom = getkeyInfo(record, '个数');
      break;
    case TopoTypeValue.CLOUD_IN_SPEC:
      dom = getkeyInfo(record, '规格');
      break;
    default:
      dom = '';
      break;
  }
  return dom;
};

// 云外数量
const getkeyInfo = (record: any, text: string) => {
  return (
    <div style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
      <div>
        <span>服务名称：</span>
        <span>{record.serviceCode}</span>
      </div>
      <div>
        <span>{`建议缩减实例${text}：`}</span>
        <span>
          {record.proposedReduceInfo}
          {text === '个数' ? '个' : ''}
        </span>
      </div>
    </div>
  );
};
