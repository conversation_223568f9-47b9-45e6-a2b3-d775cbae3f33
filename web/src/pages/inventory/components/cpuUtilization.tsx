import React, { useState, useEffect, useRef } from 'react';
import { SharkRBlock, SharkRBlockProps } from '@sharkr/components';
import { DatePicker, Radio } from 'antd';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import { getCpuUtilizationRateMap } from '../../../services/assetDial.service';

export const CpuUtilization = (props: any) => {
  const [dataSource, setDataSource] = useState<{ cloud_in: any[]; cloud_out: any[] }>({
    cloud_in: [],
    cloud_out: []
  });
  const [timeStamp, setTimeStamp] = useState<number>(0);

  const blockConfig: SharkRBlockProps = {
    id: '/block21',
    header: {
      title: '机器CPU平均利用率',
      tools: (
        <div className="sharkr-tools">
          <Radio.Group
            defaultValue={1}
            onChange={e => {
              radioChange(e);
            }}>
            <Radio value={1}>近半年</Radio>
            <Radio value={2}>近一年</Radio>
          </Radio.Group>
        </div>
      )
    },
    type: {
      level: 2,
      border: true
    }
  };

  const getOption = (data?: any) => {
    return {
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        show: true,
        icon: 'circle',
        top: 'bottom',
        data: ['云内', '云外']
      },
      grid: {
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: timeStamp
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '云内',
          type: 'line',
        //   stack: 'Total',
          data: dataSource.cloud_in
        },
        {
          name: '云外',
          type: 'line',
        //   stack: 'Total',
          data: dataSource.cloud_out
        }
      ]
    };
  };

  useEffect(() => {
    // 首次进入默认近半年
    getList(1);
  }, []);

  const radioChange = (e: any) => {
    getList(e.target.value);
  };

  const getList = (type: number) => {
    getCpuUtilizationRateMap({ type }).then(res => {
      if (res?.code === 200) {
        // 把从后端拿到的数据 转化为 ECharts 需要的格式
        const { cloud_out, cloud_in } = res.data;
        // 时间轴数据
        const timeStamp = cloud_out.map((item: any) => item.date);
        // 云内数据
        const cloudInValueList: any[] = cloud_in.map((item: any) => item.value);
        // 云外数据
        const cloudOutValueList: any[] = cloud_out.map((item: any) => item.value);
        console.log(cloudInValueList, cloudOutValueList)
        setTimeStamp(timeStamp);
        setDataSource({
          cloud_in: cloudInValueList || [],
          cloud_out: cloudOutValueList || []
        });
      }
    });
  };

  return (
    <>
      <SharkRBlock {...blockConfig}>
        <ReactEcharts option={getOption(dataSource)} style={{ height: '350px' }} />
      </SharkRBlock>
    </>
  );
};
