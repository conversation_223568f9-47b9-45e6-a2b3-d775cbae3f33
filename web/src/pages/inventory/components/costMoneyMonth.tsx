import React, { useState, useEffect, useRef } from 'react';
import { SharkRBlock, SharkRBlockProps } from '@sharkr/components';
import { DatePicker } from 'antd';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import { getCostMoneyMonth } from '../../../services/assetDial.service';

export const CostMoneyMonth = (props: any) => {
  const { coreTime } = props;
  const [dataSource, setDataSource] = useState<{ name: string; value: number }[]>();
  const [total, setTotal] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(coreTime || moment(moment().subtract(1,'month').startOf('month').format('YYYY-MM')).valueOf())

  const blockConfig: SharkRBlockProps = {
    id: '/block21',
    header: {
      title: '月成本概览(万)',
      tools: (
        <div className="sharkr-tools">
          时间月份：
          <DatePicker
            allowClear={false}
            defaultValue={moment(currentTime)}
            picker="month"
            onChange={(value: any) => {
              dateChange(value);
            }}
          />
        </div>
      )
    },
    type: {
      level: 2,
      border: true
    }
  };

  const getPieOption = (data?: any, totalNum?: number) => {
    return {
      legend: {
        itemWidth: 10,
        itemHeight: 10,
        icon: 'circle',
        // x: 'right',
        y: 'center',
        orient: 'vertical', // 设置图例排列纵向显示
        align: 'left', // 设置图例中文字位置在icon标识符的右侧
        itemGap: 20, // 设置图例之间的间距
        padding: [0, 0, 0, 10], // 设置图例与圆环图之间的间距
        bottom: '55%', // 距离下边距
        right: '40',
        formatter: function(name: any) {
          const itemObj = data.find((item: any) => item.name === name);
          return `${name} ${itemObj.value} w`;
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          }
        }
      ],
      tooltip: {
        show: true
      },
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: '45%',
          style: {
            text: `总成本\n\n${totalNum}W`,
            textAlign: 'center',
            fill: '#000',
            width: 30,
            height: 30,
            fontSize: 16,
            fontFamily: 'Microsoft YaHei'
          }
        }
      ]
    };
  };

  useEffect(() => {
    getList(currentTime);
  }, []);

  // 监听父组件传递的coreTime变化
  useEffect(() => {
    if (coreTime && coreTime !== currentTime) {
      setCurrentTime(coreTime);
      getList(coreTime);
    }
  }, [coreTime]);

  const dateChange = (value: any) => {
    getList(moment(moment(value).format('YYYY-MM')).valueOf());
  };

  const getList = (timeStamp: number) => {
    getCostMoneyMonth({ time: timeStamp }).then(res => {
      if (res?.code === 200 && res?.data?.length) {
        // 把从后端拿到的数据 转化为 ECharts 需要的格式
        // const { costMoneyFromCabinet, costMoneyFromQZ, costMoneyFromTrust } = res.data;
        // const list = [
        //   {
        //     value: costMoneyFromCabinet > -1 ? Number((costMoneyFromCabinet / 10000).toFixed(3)) : 0,
        //     name: '机柜'
        //   },
        //   {
        //     value: costMoneyFromQZ > -1 ?Number((costMoneyFromQZ / 10000).toFixed(3)) : 0,
        //     name: '轻舟云月费用'
        //   },
        //   {
        //     value: costMoneyFromTrust > -1 ?Number((costMoneyFromTrust / 10000).toFixed(3)) : 0,
        //     name: '机房托管'
        //   }
        // ];
        // setTotal(
        //   Number((((costMoneyFromCabinet > -1 ? costMoneyFromCabinet : 0) + (costMoneyFromQZ > -1 ? costMoneyFromQZ : 0) + (costMoneyFromTrust > -1 ? costMoneyFromTrust : 0)) / 10000).toFixed(3))
        // );
        // setDataSource(list);
        const list = res.data.map((item: any) => {return {
            ...item,
            value: item?.value > -1 ? Number((item?.value / 10000).toFixed(3)) : 0,
        }})
        const totalNum = list.reduce((acc: number, cur: {value: number})=>acc + cur.value, 0).toFixed(3)
        setTotal(totalNum)
        setDataSource(list)
      }
    });
  };

  return (
    <>
      <SharkRBlock {...blockConfig}>
        <ReactEcharts option={getPieOption(dataSource, total)} style={{ height: '350px' }} />
      </SharkRBlock>
    </>
  );
};
