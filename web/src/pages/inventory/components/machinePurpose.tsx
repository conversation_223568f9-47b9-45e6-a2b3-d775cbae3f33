import React, { useState, useEffect, useRef } from 'react';
import { SharkRBlock, SharkRBlockProps } from '@sharkr/components';
import { DatePicker } from 'antd';
import ReactEcharts from 'echarts-for-react';
import moment from 'moment';
import { getMachinePurpose } from '../../../services/assetDial.service';

export const MachinePurpose = (props: any) => {
  const { coreTime } = props;
  const [dataSource, setDataSource] = useState<{ name: string; value: number }[]>();
  const [total, setTotal] = useState<number>(0);
  const [currentTime, setCurrentTime] = useState<number>(coreTime || moment(moment().subtract(1,'month').startOf('month').format('YYYY-MM')).valueOf())

  const blockConfig: SharkRBlockProps = {
    id: '/block21',
    header: {
      title: '机器用途概览',
      tools: (
        <div className="sharkr-tools">
          时间月份：
          <DatePicker
            allowClear={false}
            defaultValue={moment(currentTime)}
            picker="month"
            onChange={(value: any) => {
              dateChange(value);
            }}
          />
        </div>
      )
    },
    type: {
      level: 2,
      border: true
    }
  };

  const getPieOption = (data?: any, totalNum?: number) => {
    return {
      legend: {
        itemWidth: 10,
        itemHeight: 10,
        icon: 'circle',
        // x: 'right',
        y: 'center',
        orient: 'vertical', // 设置图例排列纵向显示
        align: 'left', // 设置图例中文字位置在icon标识符的右侧
        itemGap: 20, // 设置图例之间的间距
        padding: [0, 0, 0, 10], // 设置图例与圆环图之间的间距
        bottom: '55%', // 距离下边距
        right: '40',
        formatter: function(name: any) {
          const itemObj = data.find((item: any) => item.name === name);
          return `${name} ${itemObj.value}`;
        }
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center'
          },
          data: data,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          labelLine: {
            show: false
          }
        }
      ],
      tooltip: {
        show: true
      },
      graphic: [
        {
          type: 'text',
          left: 'center',
          top: '45%',
          style: {
            text: `总机器数\n\n${totalNum}`,
            textAlign: 'center',
            fill: '#000',
            width: 30,
            height: 30,
            fontSize: 16,
            fontFamily: 'Microsoft YaHei'
          }
        }
      ]
    };
  };

  useEffect(() => {
    getList(currentTime);
  }, []);

  // 监听父组件传递的coreTime变化
  useEffect(() => {
    if (coreTime && coreTime !== currentTime) {
      setCurrentTime(coreTime);
      getList(coreTime);
    }
  }, [coreTime]);

  const dateChange = (value: any) => {
    getList(moment(moment(value).format('YYYY-MM')).valueOf());
  };

  const getList = (timeStamp: number) => {
    getMachinePurpose({ time: timeStamp }).then(res => {
      if (res?.code === 200) {
        // 把从后端拿到的数据 转化为 ECharts 需要的格式
        const {
          machineNumFromBigData,
          machineNumFromCloud,
          machineNumFromDB,
          machineNumFromHost,
          machineNumFromOther
        } = res.data;
        const list = [
          {
            value: machineNumFromBigData > -1 ? machineNumFromBigData : '无',
            name: '大数据机器数量'
          },
          {
            value: machineNumFromCloud > -1 ? machineNumFromCloud : '无',
            name: '云主机数量'
          },
          {
            value: machineNumFromDB > -1 ? machineNumFromDB : '无',
            name: '数据库机器数量'
          },
          {
            value: machineNumFromHost > -1 ? machineNumFromHost : '无',
            name: '宿主机数量'
          },
          {
            value: machineNumFromOther > -1 ? machineNumFromOther : '无',
            name: '其他机器数量'
          }
        ];
        setTotal(
          Number(machineNumFromBigData > -1 ? machineNumFromBigData : 0) +
            Number(machineNumFromCloud > -1 ? machineNumFromCloud : 0) +
            Number(machineNumFromDB > -1 ? machineNumFromDB : 0) +
            Number(machineNumFromHost > -1 ? machineNumFromHost : 0) +
            Number(machineNumFromOther > -1 ? machineNumFromOther : 0)
        );
        setDataSource(list);
      }
    });
  };

  return (
    <>
      <SharkRBlock {...blockConfig}>
        <ReactEcharts option={getPieOption(dataSource, total)} style={{ height: '350px' }} />
      </SharkRBlock>
    </>
  );
};
