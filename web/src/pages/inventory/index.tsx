import React, { useState, useEffect, useRef } from 'react';
import { RouteComponentProps } from 'react-router';
import { PlainObject } from '@shark/core';
import { floatUnitFormat, ratioFormat } from '@sharkr/util';
import { IndicatorCardGroup } from '@eagler/dp-components';
import { getCoreMetric } from '../../services/assetDial.service';
import { QuestionCircleOutlined } from '@ant-design/icons';
import './index.scss';
import { DatePicker, Tooltip } from 'antd';
import moment from 'moment';
import { MachineSource } from './components/machineSource';
import { MachinePurpose } from './components/machinePurpose';
import { CostMoneyMonth } from './components/costMoneyMonth';
import { CpuUtilization } from './components/cpuUtilization';
import { genNumberLabel } from '../../utils';

const Inventory: React.FC<RouteComponentProps> = (props: RouteComponentProps) => {
    const [coreData, setCoreData] = useState<any>(); // 数据源
    const [coreTime, setCoreTime] = useState<any>(moment(moment().subtract(1, 'month').startOf('month').format('YYYY-MM')).valueOf()); // 核心指标月份选择

    useEffect(() => {
        console.log('enter')
        getCoreMetric({ time: coreTime }).then(res => {
            if (res?.code === 200) {
                console.log(Number((res.data.costMoneyMonth / 10000).toFixed(3)))
                setCoreData({
                    ...res.data,
                    ...res.data.availableCoreNumVO,
                    costMoneyMonth: (res.data.costMoneyMonth / 10000).toFixed(3)
                });
            }
        });
    }, [coreTime]);

    const machineConfig = [
        {
            keyName: 'machineNum',
            name: '总机器量',
            format: {
                digits: 0,
                unit: null,
                thousandsSeparator: true,
                suffix: '台'
            },
            relatedIndicators: [
                {
                    keyName: 'machineNumPerCent',
                    name: '月环比',
                    format: {
                        suffix: '%'
                    },
                    hasSign: true
                }
            ]
        },
        {
            keyName: 'costMoneyMonth',
            name: '月成本',
            format: {
                digits: 3,
                unit: null,
                // thousandsSeparator: true,
                suffix: 'W'
            },
            relatedIndicators: [
                {
                    keyName: 'costMoneyMonthPerCent',
                    name: '月环比',
                    format: {
                        suffix: '%'
                    },
                    hasSign: true
                }
            ]
        },
        {
            keyName: 'lowUtilizationRateServiceNum',
            name: '低利用率服务',
            format: {
                digits: 0,
                unit: null,
                thousandsSeparator: true
            },
            relatedIndicators: [
                {
                    keyName: 'lowUtilizationRateServiceNumPerCent',
                    name: '月环比',
                    format: {
                        suffix: '%'
                    },
                    hasSign: true
                }
            ]
        },
        {
            keyName: 'availableCoreNum',
            name: '可用资源(核)',
            format: {
                digits: 0,
                unit: null,
                thousandsSeparator: true
            },
            relatedIndicators: [
                {
                    keyName: 'availableCoreNumPerCent',
                    name: '月环比',
                    format: {
                        suffix: '%'
                    },
                    hasSign: true
                }
            ]
        }
    ];

    const coreMonthChange = (value: moment.Moment | null) => {
        if (value) {
            setCoreTime(moment(moment(value).format('YYYY-MM')).valueOf());
        }
    };

    return (
        <div style={{ minWidth: '1300px' }}>
            <section className="sharkr-section">
                <div className="sharkr-section-header">
                    <span className="sharkr-section-header-title">核心指标</span>
                    <span className="sharkr-section-global-header-sub-title">
                        <Tooltip arrowPointAtCenter placement="right" title="核心指标desc">
                            <QuestionCircleOutlined className="margin-l-base" translate={null} />
                        </Tooltip>
                    </span>
                    <div className="sharkr-tools">
                        时间月份：
                        <DatePicker allowClear={false} defaultValue={moment(coreTime)} picker="month" onChange={coreMonthChange} />
                    </div>
                </div>
                <div className="sharkr-section-content">
                    <IndicatorCardGroup data={coreData} groupConfig={machineConfig} />
                    {
                        coreData ? (
                            <div className="m-core-detail">
                                <div className="u-core-item">
                                    <span className="u-core-color u-color-blue"></span>
                                    <span className="u-core-tlt">云外总核</span>
                                    <span className="u-core-cut">｜</span>
                                    <span className="u-core-num">{coreData?.coreNumFromCloudOut}</span>
                                </div>
                                <div className="u-core-item">
                                    <span className="u-core-color u-color-green"></span>
                                    <span className="u-core-tlt">云外可用</span>
                                    <span className="u-core-cut">｜</span>
                                    <span className="u-core-num">{coreData?.availableCoreNumFromCloudOut}</span>
                                </div>
                                {/* <div className="u-core-item">
                                    <span className="u-core-color u-color-yellow"></span>
                                    <span className="u-core-tlt">云内总核</span>
                                    <span className="u-core-cut">｜</span>
                                    <span className="u-core-num">{coreData?.coreNumFromCloudIn}</span>
                                </div>
                                <div className="u-core-item">
                                    <span className="u-core-color u-color-red"></span>
                                    <span className="u-core-tlt">云内可用</span>
                                    <span className="u-core-cut">｜</span>
                                    <span className="u-core-num">{coreData?.availableCoreNumFromCloudIn}</span>
                                </div> */}
                            </div>
                        ) : ''
                    }
                </div>
            </section>
            <div style={{ display: 'flex' }}>
                {/* 机器来源 */}
                <div style={{ width: '49%', marginRight: '2%' }}>
                    <MachineSource coreTime={coreTime} />
                </div>
                {/* 机器用途 */}
                <div style={{ width: '49%' }}>
                    <MachinePurpose coreTime={coreTime} />
                </div>
            </div>
            <div style={{ display: 'flex', marginTop: '25px' }}>
                {/* 月成本概览 */}
                <div style={{ width: '49%', marginRight: '2%' }}>
                    <CostMoneyMonth coreTime={coreTime} />
                </div>
                {/* 机器cpu平均利用率 */}
                <div style={{ width: '49%' }}>
                    <CpuUtilization />
                </div>
            </div>
        </div>
    );
};

export default Inventory;
