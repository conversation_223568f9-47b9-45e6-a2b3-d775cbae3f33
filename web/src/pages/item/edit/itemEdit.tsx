import React from 'react';
import { QuestionCircleOutlined, UploadOutlined, FileTextOutlined } from '@ant-design/icons';
import {
  Form,
  Button,
  Switch,
  Input,
  Select,
  DatePicker,
  Radio,
  Checkbox,
  Slider,
  Rate,
  Upload,
  Alert,
  Tooltip
} from 'antd';

const { TextArea } = Input;
const { Option } = Select;

export const ItemEdit: React.FC<any> = (props: any) => {
  const onFinish = (values: any) => {
    console.log('Success:', values);
  };

  const onFinishFailed = (errorInfo: any) => {
    console.log('Failed:', errorInfo);
  };
  return (
    <>
      <div className="sharkr-section  sharkr-section-fullscreen">
        <div className="sharkr-section-header">
          <span className="sharkr-section-header-title">新增商品</span>
        </div>
        <div className="sharkr-section-content">
          <Form
            className="sharkr-form-base"
            name="demo21"
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}>
            <Form.Item
              label="邮箱"
              name="email"
              rules={[
                {
                  type: 'email',
                  message: 'The input is not valid E-mail!'
                },
                {
                  required: true,
                  message: 'Please input your E-mail!'
                }
              ]}>
              <Input className="sharkr-w-xlg" placeholder="placeholder" />
            </Form.Item>
            <Form.Item label="联系方式" name="phone">
              <Input className="sharkr-w-xlg" placeholder="placeholder" />
            </Form.Item>
            <Form.Item
              label={<>城市</>}
              name="city"
              rules={[
                {
                  required: true,
                  message: '请选择城市'
                }
              ]}>
              <Select className="sharkr-w-md">
                <Select.Option value="北京">北京</Select.Option>
                <Select.Option value="上海">上海</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label="是否正职" name="isCommon" valuePropName="checked">
              <Switch defaultChecked />
            </Form.Item>
            <Form.Item
              label="类型"
              name="type"
              rules={[
                {
                  required: true,
                  message: '请选择类型'
                }
              ]}>
              <Radio.Group>
                <Radio value={1}>A</Radio>
                <Radio value={2}>B</Radio>
                <Radio value={3}>C</Radio>
                <Radio value={4}>D</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="标签"
              name="label"
              rules={[
                {
                  required: true,
                  message: '请选择类型'
                }
              ]}>
              <Checkbox.Group>
                <Checkbox value="A">A</Checkbox>
                <Checkbox value="B">B</Checkbox>
                <Checkbox value="C">C</Checkbox>
                <Checkbox value="D">D</Checkbox>
              </Checkbox.Group>
            </Form.Item>
            <Form.Item label="日期" name="date">
              <DatePicker />
            </Form.Item>
            <Form.Item
              label="地址"
              name="address"
              rules={[
                {
                  required: true,
                  message: '请输入地址'
                }
              ]}>
              <TextArea className="sharkr-w-xlg" rows={4} />
            </Form.Item>
            <Form.Item
              label="备选国家"
              name="service"
              rules={[
                {
                  required: true,
                  message: '请输入地址'
                }
              ]}>
              <Select className="sharkr-w-xlg" mode="multiple">
                <Option value="china">
                  <span aria-label="China" role="img">
                    🇨🇳
                  </span>
                  China (中国)备选备选备选备选
                </Option>
                <Option value="usa">
                  <span aria-label="USA" role="img">
                    🇺🇸
                  </span>
                  USA (美国)备选备选备选备选
                </Option>
                <Option value="japan">
                  <span aria-label="Japan" role="img">
                    🇯🇵
                  </span>
                  Japan (日本)备选备选备选备选
                </Option>
                <Option value="korea">
                  <span aria-label="Korea" role="img">
                    🇰🇷
                  </span>
                  Korea (韩国)备选备选备选备选
                </Option>
              </Select>
            </Form.Item>
            <Form.Item label="等级" name="level">
              <Slider
                className="sharkr-w-lg"
                marks={{
                  0: 'A',
                  20: 'B',
                  40: 'C',
                  60: 'D',
                  80: 'E',
                  100: 'F'
                }}
              />
            </Form.Item>
            <Form.Item label="评分" name="rate">
              <Rate className="sharkr-w-xlg" />
            </Form.Item>
            <Form.Item
              extra="支持拓展名： .rar .zip .doc .docx.pdf .jpg"
              label="上传文件"
              name="upload"
              valuePropName="fileList">
              <Upload action="/upload.do" listType="picture" name="logo">
                <Button>上传文件</Button>
              </Upload>
            </Form.Item>
            <Form.Item label="上传文件" name="dragger" valuePropName="fileList">
              <Upload.Dragger action="/upload.do" className="disp-block sharkr-w-lg">
                <p className="ant-upload-drag-icon"></p>
                <p className="ant-upload-text">点击或将文件拖拽到这里上传</p>
                <p className="ant-upload-hint">支持拓展名： .rar .zip .doc .docx.pdf .jpg</p>
              </Upload.Dragger>
            </Form.Item>
            <Form.Item className="sharkr-form-base-no-label margin-b-0">
              <div className="sharkr-tools left">
                <Button className="tool" htmlType="submit" type="primary">
                  保存
                </Button>
                <Button className="tool"> 重置</Button>
              </div>
            </Form.Item>
          </Form>
        </div>
      </div>
      <div className="sharkr-form-fixed-action">
        <div className="sharkr-tools">
          <Button className="tool" type="primary">
            保存
          </Button>
          <Button className="tool"> 重置</Button>
        </div>
      </div>
    </>
  );
};
