import React from 'react';
import { withRouter } from 'react-router-dom';
import { HomeOutlined } from '@ant-design/icons';
import { Menu, Badge, Input } from 'antd';
import { SharkRLayout, SharkRMenuProps } from '@sharkr/components';
import { filterDataByUmc } from '@eagler/authorizion';
import { getUserInfo } from '../../services';
import { logout } from '../../utils';
import { BaseLayoutProps } from './interface';

const { Header, Container, Sider, Content } = SharkRLayout;
const { Left, Right, Menu: HeaderMenu, DropDown: HeaderDropDown } = Header;
const { Menu: SiderMenu, Logo: SiderLogo } = Sider;
const { Search } = Input;

export const LayoutBase: React.FC<BaseLayoutProps> = (props: BaseLayoutProps) => {
  const userMenu = (
    <Menu>
      <Menu.Item onClick={() => logout()}>退出</Menu.Item>
    </Menu>
  );

  return (
    <SharkRLayout mode="horizontal">
      <Sider theme="dark">
        <SiderLogo desc="TAM资产管理平台" product={props.product || 'TAM'} />
        <SiderMenu
          {...(props.siderMenu as SharkRMenuProps)}
          dataFilter={filterDataByUmc}
          theme="dark"
        />
      </Sider>
      <Container>
        <Header theme="light">
          <Left>
            {props.headerMenu && (
              <Left>
                <HeaderMenu {...props.headerMenu} />
              </Left>
            )}
          </Left>
          <Right>
            <div className="sharkr-layout-header-tools">
              {/* <div className="tool">
                <Search className="sharkr-w-md" />
              </div>
              <div className="tool">
                <a href="">业务门户</a>
              </div>
              <div className="tool">
                <Badge count={5}>
                  <a href="">产品反馈</a>
                </Badge>
              </div> */}
              <div className="tool">
                <a href="https://kttfkmbfmy.feishu.cn/wiki/J0EqwaqrYiShBUk38gkc6RhZncd" target="_blank">使用文档</a>
              </div>
              <div className="tool">
                <HeaderDropDown overlay={userMenu} title={getUserInfo().name} />
              </div>
            </div>
          </Right>
        </Header>
        <Content className="body-transparent">{props.children}</Content>
      </Container>
    </SharkRLayout>
  );
};

export const BaseLayout = withRouter((props: any) => <LayoutBase {...props} />);
