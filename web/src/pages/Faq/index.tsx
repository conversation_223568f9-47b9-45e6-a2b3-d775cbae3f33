import { SharkRBlock, SharkRBlockProps } from '@sharkr/components';
import React, { useState, useEffect, useRef } from 'react';
import { RouteComponentProps } from 'react-router';
import { Breadcrumb } from '../../components/breadcrumb';
import { getFaq } from '../../services/faq.service';

const FAQ: React.FC<RouteComponentProps> = (props: RouteComponentProps) => {
    const [faqList, setFaqList] = useState([])

    useEffect(() => {
        getFaq().then(res => {
            console.log(res)
            if (res?.code === 200 && res?.data?.length) {
                console.log(res.data)
                setFaqList(res.data)
            }
        })
    }, [])

    const blockConfig = (name: string) => {
        const itemConfig: SharkRBlockProps = {
            id: `/${name}`,
            header: {
                title: name,
            },
            type: {
                level: 2,
            }

        };
        return itemConfig
    }

    return (
        <>
            <Breadcrumb breadCrumb={[{ label: '说明', path: '' }]} />
            <section className="sharkr-section" style={{ minWidth: '1250px' }}>
                <div className="sharkr-section-header">
                    <span className="sharkr-section-header-title">FAQ</span>
                </div>
                <div className="sharkr-section-content">
                    {
                        faqList?.length ? faqList.map((item: any) => (
                            <SharkRBlock key={item.question} {...blockConfig(item?.question)}>
                                {item?.answer}
                            </SharkRBlock>
                        )) : (
                            <SharkRBlock {...blockConfig('暂无问题')}>
                                -
                            </SharkRBlock>
                        )
                    }
                </div>
            </section>
        </>
    )
}

export default FAQ;