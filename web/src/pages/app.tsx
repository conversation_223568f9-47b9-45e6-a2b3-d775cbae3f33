import React from 'react';
import { message } from 'antd';
import { HashRouter as Router } from 'react-router-dom';
import {
  SharkRMenuProps,
  SharkRMenuItemClickEvent,
  SharkRMenuItem,
  SharkRIcon
} from '@sharkr/components';
import { BaseLayout } from './layouts';
import './app.scss';
import { AppRoute } from './appRoute';

export const App: React.FC = () => {
  const siderMenu: SharkRMenuProps = {
    data: [
      {
        name: '权限管理',
        icon: 'sharkr-quanxian',
        authCondition: '1',
        children: [
          {
            name: '组织身份',
            icon: 'item',
            activeRouters: ['#/icac/orgInfoList'],
            authCondition: '5',
            link: {
              href: '#/icac/orgInfoList'
            }
          },
          {
            name: '用户列表',
            icon: 'item',
            activeRouters: ['#/icac/userList'],
            authCondition: '2',
            link: {
              href: '#/icac/userList'
            }
          },
          {
            name: '角色列表',
            icon: 'item',
            authCondition: '3',
            link: {
              href: '#/icac/roleList'
            }
          },
          {
            name: '权限列表',
            icon: 'item',
            authCondition: '4',
            link: {
              href: '#/icac/permList'
            }
          }
        ]
      },
      {
        name: '工单管理',
        icon: 'sharkr-gongzuotai',
        authCondition: '6',
        children: [
          {
            name: '工单列表',
            icon: 'item',
            authCondition: '7',
            link: {
              href: '#/workflow/admin'
            }
          },
          {
            name: '我的工单',
            icon: 'item',
            authCondition: '8',
            link: {
              href: '#/workflow/list'
            },
            activeRouters: ['#/workflow/approve']
          }
        ]
      },
      {
        name: '资产大盘',
        icon: 'sharkr-wuliaoguanli',
        authCondition: '9',
        link: {
          href: '#/inventory'
        }
      },
      {
        name: '部门资产',
        icon: 'sharkr-baojia',
        authCondition: '10',
        link: {
          href: '#/assets/overall'
        },
        activeRouters: ['#/assets/detail']
      },
      {
        name: '低利用率资产',
        icon: 'sharkr-baojia',
        authCondition: '11',
        link: {
          href: '#/lowAvail'
        }
      },
      {
        name: 'FAQ',
        icon: 'sharkr-zhaobiaoguanli',
        authCondition: '12',
        link: {
          href: '#/FAQ'
        }
      }
      // {
      //     name: '首页',
      //     icon: 'home',
      //     authCondition: 100,
      //     link: {
      //         href: '#/home'
      //     }
      // },
      // {
      //     name: '商品中心',
      //     icon: 'sharkr-shangpinxinxi',
      //     authCondition: 200,
      //     children: [
      //         {
      //             name: '商品列表',
      //             icon: 'item',
      //             authCondition: 2001,
      //             activeRouters: ['#/item/edit'],
      //             link: {
      //                 href: '#/item/list'
      //             }
      //         },
      //         {
      //             name: '商品详情',
      //             icon: 'item',
      //             authCondition: 2002,
      //             link: {
      //                 href: '#/item/detail'
      //             }
      //         }
      //     ]
      // },
      // {
      //     name: '拦截链接跳转',
      //     icon: 'sharkr-xitongguanli',
      //     authCondition: 300,
      //     link: { href: '#/stop' },
      //     onItemClick: (e: SharkRMenuItemClickEvent) => {
      //         // 自定义回调事件
      //         message.info('拦截跳转');
      //         return false;
      //     }
      // },
      // {
      //     name: '自定义render',
      //     link: { href: '#/set-render' },
      //     render: (item: SharkRMenuItem) => (
      //         <>
      //             <SharkRIcon type="sharkr-gongzuotai" />
      //             <span title="自定义render">
      //                 {item.name}
      //                 <span style={{ color: '#E7422B' }}>（10）</span>
      //             </span>
      //         </>
      //     )
      // }
    ]
  };

  return (
    <Router>
      <BaseLayout siderMenu={siderMenu}>
        <AppRoute />
      </BaseLayout>
    </Router>
  );
};
