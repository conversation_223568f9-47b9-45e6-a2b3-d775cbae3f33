import React from 'react';
import ReactDOM from 'react-dom';
import { axiosService } from '@sharkr/request';
import './index.scss';
import { AppConfig } from '@shark/core';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';
import { App } from './pages/app';
import { filterCode } from './utils';
import * as serviceWorker from './serviceWorker';

// 全局配置
AppConfig.configure({
  contextPath: '/tam-web',
  productCode: 'yx-tam'
});
// 设置code过滤
axiosService.setFilterCode(filterCode);

ReactDOM.render(
  <ConfigProvider locale={zhCN}>
    <App />
  </ConfigProvider>,
  document.getElementById('root')
);

// If you want your app to work offline and load faster, you can change
// unregister() to register() below. Note this comes with some pitfalls.
// Learn more about service workers: https://bit.ly/CRA-PWA
serviceWorker.unregister();
