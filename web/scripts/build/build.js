/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path');
const argv = require('yargs').argv;

//webpack 配置
const sharkrScripts = require('@sharkr/scripts');

const sw = new sharkrScripts();
const sharkConf = sw.config;
const webpackConfig = sw.getBuildConfig(argv.target);
const htmlWebpackPlugin = sw.getModule('html-webpack-plugin');

// 如果多页面
webpackConfig.entry.login = [path.join(sharkConf.appSrc, 'login')];
webpackConfig.plugins.unshift(
    new htmlWebpackPlugin({
        filename: 'login.html',
        template: path.join(sharkConf.appPublic, 'login.html'),
        chunks: ['login'],
    })
);

// 在此处可以配置 build 过程中需要外接的 config 文件

sw.runBuild({
    webpackConfig: webpackConfig, // optional
    target: argv.target, // required
    callback: () => {}, // optional
});
