{"code": 200, "data": [{"departmentFullName": "基础技术部-中台技术组", "fourthDepartmentId": 50, "fourthDepartmentName": "陆熠彤", "thirdDepartmentId": 56, "thirdDepartmentName": "杨烨伟"}, {"departmentFullName": "基础技术部-业务前端组", "fourthDepartmentId": 51, "fourthDepartmentName": "陆熠彤", "thirdDepartmentId": 56, "thirdDepartmentName": "杨烨伟"}, {"departmentFullName": "基础技术部-稳定组", "fourthDepartmentId": 52, "fourthDepartmentName": "陆熠彤", "thirdDepartmentId": 56, "thirdDepartmentName": "杨烨伟"}], "error": {"description": "", "error": {"pariatur1d": -81728258, "non_f": "aliqua consequat proident et qui"}, "errorCode": 66}, "message": "Msg消息，字符串格式，非必须  \n可供调用方直接对终端用户输出和使用的业务报错信息 业务异常必须，其它非必须，不允许在此存放堆栈信息等无意义信息\n"}