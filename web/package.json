{"name": "tam-web", "version": "0.0.1", "private": true, "dependencies": {"@eagler/authorizion": "^2.0.0", "@eagler/dp-components": "^1.6.33", "@eagler/icac-bu": "~1.0.7", "@eagler/popo": "0.0.1", "@eagler/umc-select-web": "^2.0.0", "@eagler/workflow": "^8.5.7", "@shark/core": "^1.1.3", "@sharkr/components": "^2.6.9", "@sharkr/css": "^2.0.0", "@sharkr/request": "^1.1.0", "antd": "^4.0.0", "browserslist": "4.6.3", "echarts": "^5.1.2", "echarts-for-react": "^3.0.1", "react": "16.12.0", "react-css-modules": "^4.7.11", "react-dom": "16.12.0", "react-router": "5.1.2", "react-router-dom": "5.1.2"}, "scripts": {"dev": "sh scripts/dev/dev.sh", "build": "sh scripts/build/build.sh", "build:test": "sh scripts/build/build-test.sh", "build:online": "sh scripts/build/build-online.sh", "lint": "eslint --ext=jsx,ts,tsx src", "lint-fix": "eslint --ext=jsx,ts,tsx src --fix"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@sharkr/eslint-config-react": "^2.0.6", "@sharkr/scripts": "^2.1.0", "@types/jest": "24.0.13", "@types/lodash.debounce": "^4.0.7", "@types/node": "12.0.8", "@types/react": "16.8.19", "@types/react-dom": "16.8.4", "@types/react-router": "5.1.2", "@types/react-router-dom": "5.1.2", "browserslist": "4.6.2", "eslint": "^6.8.0", "get-port": "^5.0.0", "koa": "^2.7.0", "koa-body": "^4.1.0", "koa-proxies": "^0.8.1", "koa-webpack-middleware": "^1.0.7", "typescript": "4.3.2"}}