## 一、应用配置所在

### 1. server 配置

#### ./server/src/conf/\*

区分环境的服务器配置，路径、端口，转发配置

### 2. OPERA 发布配置

#### ./deploy/env/\*.sh

配置环境变量 NODE_ENV，consult标签、服务端口、以及其它 opera 发布时需要用到的配置


## 二、命令说明

### 1. 前端运行方式

```bash
$ cd web && npm run dev

配置host：
127.0.0.1 local.yx.mail.netease.com
127.0.0.1 remote.yx.mail.netease.com

- 本地mock开发 访问local.yx.mail.netease.com
- 联调模式 访问remote.yx.mail.netease.com
```

### 2. 后端运行方式

```bash
$ cd server && npm run server
```

### 3. 打包-本地包

```bash
$ npm run build
```

### 4. 打包-测试包

```bash
$ npm run build:test
```

### 5. 打包-线上包

```bash
$ npm run build:online
```

## 三、持续集成

### 1. 启用 git-ci

#### gitlab->project->settings->ci/cd->runners settings


## 四、效率工具

#### 1. 全局安装 @sharkr/schematic-cli
```bash
$ npm run  @sharkr/schematic-cli -g
```

#### 2. 直接体验模板工程中的工具包 ./schematics/\*

```bash
$ cd schematics/my_first 
$ npm run build 
$ srs-init .:my-first --index=1 --name=me --dry-run=false
 ```

#### 3. 创建自己的工具包 ./schematics/\*

3.1 @sharkr/schematic-cli自带了两个schematics 模板
运行以下命令进行查看
```bash
$ srs-init --list
```
3.2  选用其中的一个模板创建我们自己的工具包
```bash
$ cd schematics && srs-init blank --name=my_first
```
3.3 进行my_first目录进行开发、编译

```bash
$ cd schematics/my_first && npm run build
 ```
3.4 运行自己的 schematic,

```base
$ cd schematics/my_first && srs-init .:my-first
```

## 五、多页面配置
如果要新增一个页面入口，以增加一个登录页为例，需要做以下几步：

### 1. 新增一个login页面
web -> public -> login.html

### 2. 新增入口文件
web -> src -> login.tsx

### 3. 新增一个文件夹
web -> src -> login -> ...

### 4. dev server的webpack增加入口
web -> scripts -> dev -> server.js

```js
const htmlWebpackPlugin = sw.getModule('html-webpack-plugin');

// 如果多页面
webpackConfig.entry.login = [path.join(sharkConf.appSrc, 'login')].concat([
    'webpack-hot-middleware/client?reload=true',
]);
webpackConfig.plugins.unshift(
    new htmlWebpackPlugin({
        filename: 'login.html',
        template: path.join(sharkConf.appPublic, 'login.html'),
        chunks: ['login'],
    })
);
```

### 5. build的webpack增加入口
web -> scripts -> build -> build.js

```js
const htmlWebpackPlugin = sw.getModule('html-webpack-plugin');

// 如果多页面
webpackConfig.entry.login = [path.join(sharkConf.appSrc, 'login')];
webpackConfig.plugins.unshift(
    new htmlWebpackPlugin({
        filename: 'login.html',
        template: path.join(sharkConf.appPublic, 'login.html'),
        chunks: ['login'],
    })
);
```