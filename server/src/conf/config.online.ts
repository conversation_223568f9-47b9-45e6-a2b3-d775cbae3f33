import { ITigerProxyOption } from '@tiger/proxy';
import BaseConfig from './config.base';
import { join } from 'path';
import { AppModuleConfig } from './types';

// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
export default class Config extends BaseConfig {
    loggerPath: string = join('/home/<USER>/', this.serviceCode);
    topoDomains:string= 'http://127.0.0.1:8550/proxy/online.yanxuan-flowx-server.service.mailsaas';
    // app自己的转发配置，需要开发自己改成自己应用的 TODO
    appProxyOptions: ITigerProxyOption = {
        target:'http://127.0.0.1:8550/proxy/online.tam-server.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/appService`
                ),
                ''
            );
        }
    };


    umcProxyOptions: ITigerProxyOption = {
        target:
            'http://127.0.0.1:8550/proxy/online.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
                ),
                ''
            );
        }
    };
    
    icacbuProxyOptions: ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/online.yanxuan-ius.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/eagler/icacBu/iusServer`
                ),
                ''
            );
        }
    };

    snestProxyOptions: ITigerProxyOption = {
        target: '127.0.0.1:8550/proxy/online.snest-atlas.service.mailsaas/',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/snestService/xhr`
                ),
                ''
            );
        }
    };

    cmdbServerProxyOptions:ITigerProxyOption = {
        target: 'http://127.0.0.1:8550/proxy/online.yanxuan-cmdb-api.service.mailsaas',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/cmdbServer`
                ),
                ''
            );
        }
    }

    modules: AppModuleConfig = {
        '@tiger/security': {
            enable: true,
            options: {
                csrf: true,
                'Strict-Transport-Security': true,
                'X-Frame-Options': true
            }
        },
        '@tiger/swagger': {
            enable: false
        }
    };
}
