import { ITigerProxyOption } from '@tiger/proxy';
import BaseConfig from './config.base';
import { join } from 'path';

// @EnableApolloConfig('application') 这个注解给apollo配置中心的时候用
export default class Config extends BaseConfig {
    loggerPath: string = join(this.rootPath, 'log');
    topoDomains: string = 'http://flowx.test.you.163.com/';
    appProxyOptions: ITigerProxyOption = {
        target: 'http://test.yx.mail.netease.com',
        changeOrigin: true,
        autoRewrite: true,
        headers: {},
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/appService`
                ),
                `${this.contextPath}${this.xhrPrefix}/appService`
            );
        }
    };
    umcProxyOptions: ITigerProxyOption = {
        target: 'http://************',
        changeOrigin: true,
        autoRewrite: true,
        headers: { Host: 'yxius.you.163.com' },
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/userCenterManage`
                ),
                ''
            );
        }
    };

    icacbuProxyOptions: ITigerProxyOption = {
        target: 'http://yxius.you.163.com',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/eagler/icacBu/iusServer`
                ),
                ''
            );
        }
    };

    snestProxyOptions: ITigerProxyOption = {
        target: 'http://test.yx.mail.netease.com',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}/snestService/xhr`
                ),
                '/snest/xhr/snestServer'
            );
        }
    };

    cmdbServerProxyOptions:ITigerProxyOption = {
        target: 'http://test.yx.mail.netease.com',
        changeOrigin: true,
        autoRewrite: true,
        rewrite: (path: string) => {
            return path.replace(
                new RegExp(
                    `^${this.contextPath}${this.xhrPrefix}`
                ),
                `/ape${this.xhrPrefix}`
            );
        }
    }
}
