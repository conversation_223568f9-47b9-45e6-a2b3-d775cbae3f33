import { BaseWorkflowQuery } from '@eagle/workflow-node';
import { Service } from '@tiger/boot';
import axios from '@tiger/request';
import * as config from '../../../conf';

@Service
export default class WorkflowQuery extends BaseWorkflowQuery {
    constructor() {
        super(config.productCode);
    }
    async getDetail(flowId: string) {
        const topoUrl = `${config.topoDomains}/${config.productCode}/workOrder/v1/${flowId}/detail`;
        const topoData = await axios.get(topoUrl);
        return topoData.data.data;
    }

    async getTopo(topoId: string, version: number) {
        let topoUrl = `${config.topoDomains}/xhr/topoGraph/v1/topo?topoId=${topoId}`;
        if (typeof version === 'number') {
            topoUrl += `&version=${version}`;
        }
        const topoData = await axios.get(topoUrl);
        return topoData.data.data;
    }
    handleNewDataToOldTopo(data: any) {
        const {
            bpmnData: {
                model
            },
            flowxData
        } = data;
        let currentNodeId;
        const { currNodeDataList = [], opLogList = [], createUser = {} } = flowxData && flowxData.flowMeta || {};
        const acceptorList: any = [];
        const acceptorNameList: any = [];
        if (Array.isArray(currNodeDataList) && currNodeDataList.length) {
            const currNode = currNodeDataList[0];
            currentNodeId = currNode.nodeId;
            if (Array.isArray(currNode.acceptorUserList)) {
                currNode.acceptorUserList.forEach((acceptor: any) => {
                    acceptorList.push(acceptor.uid);
                    acceptorNameList.push(acceptor.userName);
                });
            }
        }
        let oplogs = [];
        if (Array.isArray(opLogList)) {
            oplogs = opLogList.map((item) => {
                const oplog = JSON.parse(item);
                return oplog;
            });
        }
        const rstData: any = {
            bpmData: {
                model: JSON.parse(model)
            },
            oplogs,
            flowData: null,
            flowxData
        };
        if (flowxData && flowxData.flowMeta && flowxData.flowMeta.flowId) {
            let status = 0;
            if (flowxData.flowMeta.currNodeDataList) {
                if (flowxData.flowMeta.currNodeDataList.length === 0) {
                    status = 200;
                } else if (flowxData.flowMeta.currNodeDataList.length > 0) {
                    status = 100;
                } else {
                    status = flowxData.flowMeta.status;
                }
            } else {
                status = flowxData.flowMeta.status;
            }
            rstData.flowData = {
                flowMetaData: {
                    currentNodeId: currentNodeId || '9999',
                    nextNodeList: (currNodeDataList || []).map((node: any) => node.nodeId),
                    nodeId: '21116301',
                    status: !currentNodeId || currentNodeId === '9999' ? 200 : status
                },
                flowNodeMessage: {
                    createUserManager: createUser.leaderUid,
                    createUserManagerName: createUser.leaderUserName,
                    createUser: createUser.uid,
                    createUserName: createUser.userName,
                    createUserOrg: createUser.userOrg,
                    acceptorList,
                    acceptorNameList,
                    ...flowxData
                }
            };
        }
        return rstData;
    }
}
